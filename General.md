# General
- The codebase uses Vue.js frontend with Naive UI components, Node.js/Hono backend, Pinia for state management, Zod for validation, and follows a pattern of comprehensive testing including unit tests for services, API integration tests, and frontend component tests.

# Testing & Debugging
- For debug reports in testing environments, it's acceptable to capture authenticated user information (user ID, email, username) when available, while maintaining graceful handling for unauthenticated submissions and preserving privacy-conscious design for production.

# Technology Stack & Conventions

*   **Core Stack:** This project uses TypeScript end-to-end. The backend is built with Node.js and Hono, and the frontend uses Vue.js 3 with the Composition API (`<script setup>`).
*   **Database:** We use Prisma as the ORM with PostgreSQL for both production and development. Remember to use Prisma Client for database interactions and `npx prisma migrate dev` for schema changes.
*   **Real-time:** Socket.IO is used extensively for secure in-app chat, transaction updates, and real-time notifications with a centralized socket management system.
*   **Internationalization (i18n):** The application fully supports multiple languages using Vue I18n:
    *   **Languages:** English (EN) and Persian (FA) with complete translations
    *   **Dynamic Switching:** Users can switch languages on-the-fly using the LanguageSelector component
    *   **Persistent:** Language preference is stored in localStorage
    *   **Right-to-Left Support:** Full RTL layout support for Persian language
*   **Theme System:**
    *   **Modes:** Supports both light and dark themes
    *   **Dynamic Switching:** Users can toggle themes using the ThemeToggle component
    *   **Persistent:** Theme preference is stored in localStorage
    *   **Naive UI Integration:** Themes are fully integrated with Naive UI components
    *   **CSS Variables:** Custom theme variables for consistent styling across the application
*   **Validation:** Zod is used for schema declaration and validation, particularly for API inputs/outputs.
*   **Package Manager:** Use `npm` as the primary package manager for both `backend` and `frontend`. Use `npm install`, `npm run dev`, `npm test`.
*   **Internationalization:** Vue I18n is implemented with support for Persian (FA) and English (EN) languages.
*   **Frontend:** Key frontend libraries include Pinia for state management, Vue Router for routing, **Naive UI** for the component library, and Vue I18n for internationalization. Code should follow Vue 3 Composition API best practices.
    *   **UI Components:**
        *   **Primary UI Library:** Naive UI is the main component library. Use Naive UI components by default for consistency and maintainability.
        *   **Custom Components:** While most UI elements use Naive UI, some specialized features require custom-built components. Custom components should:
            *   Only be created when Naive UI components can't meet specific requirements
            *   Follow the same design language as Naive UI
            *   Support both light and dark themes
            *   Be fully responsive
            *   Include comprehensive unit tests
    *   **Vue Template Comments:** **Avoid inserting any comments directly within the `<template>...</template>` block of Vue components.** If a comment is absolutely necessary within the template (e.g., to comment out a block of HTML), use HTML-style comments: `<!-- This is a valid comment -->` and ensure they are placed *between* elements or attributes, not *within* tag definitions or attribute lists. For clarity, prefer to keep templates comment-free. Comments within `<script>` tags should follow standard JavaScript/TypeScript conventions.
*   **Code Style:** Follow standard TypeScript and Vue.js code style conventions (e.g., consistent indentation, naming conventions). Prefer single quotes for strings in TypeScript/JavaScript where applicable, unless double quotes are necessary (e.g., JSON).
*   **Testing:** Tests are written using Vitest. Use `npm test` in the respective `backend` or `frontend` directory to run tests. **For frontend testing, strictly follow the validated and comprehensive guidelines in `C:\Code\MUNygo\frontend\unit.md` which have been proven through practical implementation and establish authoritative best practices for Vue 3 + TypeScript + Pinia + Naive UI testing patterns.**
*   **Development Environment:** Development is done on Windows 11. Ensure any terminal commands provided (e.g., for `npm`, `npx`, `git`) are compatible with Windows terminals (like PowerShell or Command Prompt within VS Code). Use backslashes (`\`) for local paths where appropriate.
*   **Type Safety:** All generated TypeScript and Vue code **must** be type-safe. Leverage TypeScript features like interfaces, types, generics, and strict null checks to ensure correctness and prevent runtime type errors.

# Integrations
- For Google AI SDK integration, use npm install @google/genai package and check latest documentation before implementation.
- User prefers using specific Gemini model versions like gemini-2.5-flash-preview-05-20 rather than generic model names.

# UI Preferences
- User prefers glassmorphism effects for status bars with semi-transparent dark blueish backgrounds, backdrop blur, and enhanced shadows to create visual separation from content underneath, giving the appearance that content goes beneath the status bar. User prefers glassmorphism effects to have visible background blur-through rather than solid backgrounds.
- User prefers mini/shrunk status bars.
- For timer displays, status bars should show HH:MM format while main payment sections should show HH:MM:SS format, and in RTL layouts only the timer should be repositioned to the left while maintaining proper RTL text direction for other content.
- User prefers Persian currency amounts to be displayed in millions format (e.g., '28.5M IRR' instead of '28,500,000 IRR') to save horizontal space.
- Timer positioning should follow RTL/LTR layout direction (left side for RTL, right for LTR).
- Expect proper dark/light theme implementation in TransactionView component.
- User prefers mobile-first optimization approach that prioritizes fitting components within standard mobile viewports without scrolling, favoring removal of non-essential sections (like file uploads and extra notes) and compact, space-efficient designs over feature completeness for mobile interfaces.
- For transaction status messages, user prefers contextually accurate messaging that dynamically reflects current transaction steps, distinguishing between user action states vs waiting states, with proper i18n support and Persian currency amounts in millions format. User prefers transaction status messages to be personal and user-centric, focusing on what the current user will receive rather than technical details about what others need to send, making transactions feel more human and less mechanical.
- User prefers celebratory confetti animations for successful transactions that are brief (few seconds), theme-compatible, responsive, non-interfering with UI, and implemented using lightweight libraries compatible with Vue 3/Naive UI/TypeScript stack.

# System Messages
- System messages should always include contextual information: WHO performed the action (specific username), WHAT exactly happened (detailed action description), WHEN relevant (timing/sequence), and ADDITIONAL CONTEXT (amounts, deadlines, next steps) to provide clear, unambiguous information about events and actors.