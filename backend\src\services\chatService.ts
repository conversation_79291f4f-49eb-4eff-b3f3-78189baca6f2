import { PrismaClient } from '@prisma/client';
import { Server, Socket } from 'socket.io';
import {
  CHAT_MESSAGE_RECEIVE,
  CHAT_MESSAGE_SEND,
  SYSTEM_MESSAGE_RECEIVE,
  type ChatMessageSendPayload,
  type ChatMessageReceivePayload,
  type SystemMessagePayload,
} from '../types/socketEvents';
import { getPrismaClient } from '../utils/database';

const prisma = getPrismaClient(); // Use shared PrismaClient singleton

export class ChatService {
  private io: Server;

  constructor(io: Server) {
    this.io = io;
    console.log('[ChatService] Initialized');
  }

  public async handleChatMessageSend(socket: Socket, payload: ChatMessageSendPayload) {
    const senderId = socket.data.userId as string;

    if (!senderId) {
      console.error('[ChatService] Error: Sender ID not found on socket.');
      return;
    }

    const { chatSessionId, messageText } = payload; // Corrected: content to messageText

    if (!chatSessionId || !messageText) { // Corrected: content to messageText
      console.error('[ChatService] Error: chatSessionId and messageText are required.', payload);
      return;
    }

    try {
      // 1. Verify the chat session exists and the sender is a participant
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        include: {
          userOne: true, // Offer creator
          userTwo: true, // Interested user
        },
      });

      if (!chatSession) {
        console.error(`[ChatService] Error: Chat session not found: ${chatSessionId}`);
        return;
      }

      if (senderId !== chatSession.userOneId && senderId !== chatSession.userTwoId) {
        console.error(`[ChatService] Error: Sender ${senderId} is not a participant in chat session ${chatSessionId}`);
        return;
      }

      const senderUser = await prisma.user.findUnique({ where: { id: senderId } });
      if (!senderUser) {
        console.error(`[ChatService] Error: Sender user not found: ${senderId}`);
        return;
      }

      // 2. Save the message to the database
      const message = await prisma.chatMessage.create({
        data: {
          chatSessionId,
          senderId,
          content: messageText, // Corrected: content to messageText
          isSystemMessage: false,
        },
        include: {
          sender: { // Include sender details for the payload
            select: {
              id: true,
              username: true,
              reputationLevel: true,
            },
          },
        },
      });

      // 3. Prepare the payload for broadcasting
      // Ensure sender is not null before accessing its properties
      if (!message.sender) {
        console.error(`[ChatService] Error: Sender details missing for message ID: ${message.id}`);
        // Potentially handle this by not broadcasting or sending an error message
        return;
      }
      
      const receivePayload: ChatMessageReceivePayload = {
        messageId: message.id,
        chatSessionId: message.chatSessionId,
        sender: {
          id: message.sender.id, // Null check handled by previous block
          username: message.sender.username ?? 'Unknown User', // Provide a fallback for username
          reputationLevel: message.sender.reputationLevel ?? 0, // Provide a fallback for reputationLevel
        },
        content: message.content,
        createdAt: message.createdAt.toISOString(),
        isSystemMessage: message.isSystemMessage,
      };

      // 4. Emit the message to both participants in the chat session
      // The participants are already in rooms named by their user IDs
      this.io.to(chatSession.userOneId).emit(CHAT_MESSAGE_RECEIVE, receivePayload);
      if (chatSession.userOneId !== chatSession.userTwoId) { // Avoid sending twice if user is chatting with themselves (edge case)
        this.io.to(chatSession.userTwoId).emit(CHAT_MESSAGE_RECEIVE, receivePayload);
      }
      
      console.log(`[ChatService] Message from ${senderId} sent in session ${chatSessionId}: ${messageText}`);

    } catch (error) {
      console.error(`[ChatService] Error handling CHAT_MESSAGE_SEND for session ${chatSessionId}:`, error);
    }
  }

  public async addSystemMessageToChat(chatSessionId: string, content: string, transactionId?: string | null, data?: Record<string, any>): Promise<void> {
    try {
      // 1. Fetch the chat session to identify participants
      const chatSession = await prisma.chatSession.findUnique({
        where: { id: chatSessionId },
        select: { userOneId: true, userTwoId: true },
      });

      if (!chatSession) {
        console.error(`[ChatService] Error: Chat session not found for system message: ${chatSessionId}`);
        return;
      }

      const message = await prisma.chatMessage.create({
        data: {
          chatSessionId,
          content,
          isSystemMessage: true,
          senderId: null, // System messages don't have a user sender
          transactionId: transactionId ?? undefined,
        },
      });

      const systemMessagePayload: SystemMessagePayload = {
        messageId: message.id,
        chatSessionId: message.chatSessionId,
        content: message.content,
        createdAt: message.createdAt.toISOString(),
        isSystemMessage: true,
        transactionId: message.transactionId,
        data: data, // Include translation parameters for rich contextual messages
      };
      
      // Emit to the userId rooms of both participants
      if (chatSession.userOneId) {
        this.io.to(chatSession.userOneId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      }
      // Ensure not to send twice if userOneId and userTwoId are the same (e.g., user chatting with themselves, though unlikely for system messages)
      // And ensure userTwoId exists
      if (chatSession.userTwoId && chatSession.userTwoId !== chatSession.userOneId) {
        this.io.to(chatSession.userTwoId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      }
      
      console.log(`[ChatService] System message sent to participants of chat session ${chatSessionId}`);
    } catch (error) {
      console.error(`[ChatService] Error adding system message to session ${chatSessionId}:`, error);
      // Optionally, rethrow or handle more gracefully
    }
  }
}

// Modify how chatService is initialized and exported in src/index.ts if necessary
// to make its instance or methods accessible to PayerNegotiationService.
// For example, you might export the instance from index.ts:
//
// import { ChatService } from './services/chatService';
// export const chatServiceInstance = new ChatService(io);
//
// Then import it in PayerNegotiationService:
// import { chatServiceInstance } from '../index';
