import { PrismaClient, Transaction, TransactionStatus, ChatMessage } from '@prisma/client';
import { Server } from 'socket.io';
import { NotificationService } from './notificationService';
import { ChatService } from './chatService';
import { TransactionService } from './transactionService';
import { PayerNegotiationService } from './payerNegotiationService';
import { 
  TRANSACTION_STATUS_UPDATED, 
  SYSTEM_MESSAGE_RECEIVE,
  CHAT_MESSAGE_RECEIVE,
  type TransactionStatusUpdatePayload,
  type SystemMessagePayload,
  type ChatMessageReceivePayload
} from '../types/socketEvents';

interface TransactionChatStep {
  id: number;
  key: string;
  titleKey: string;
  isUserAction: boolean;
  status: TransactionStatus;
}

interface TransactionChatDetails {
  id: string;
  chatSessionId: string;
  offerId: string | null;
  status: TransactionStatus;
  currencyA: string;
  amountA: number;
  currencyAProviderId: string;
  currencyB: string;
  amountB: number;
  currencyBProviderId: string;
  agreedFirstPayerId: string | null;
  otherUser: {
    id: string;
    name: string;
    profilePic?: string | null;
    reputation: number;
  };
  transactionDetails: {
    amountToSend: number;
    amountToReceive: number;
    currencyFrom: string;
    currencyTo: string;
    isUserFirstPayer: boolean;
    otherUserPaymentDetails?: any;
    userPaymentDetails?: any;
  };
  currentStepIndex: number;
  feedItems: TransactionFeedItem[];
  timer?: {
    isActive: boolean;
    remainingSeconds: number;
    dueDate?: string;
  };
}

interface TransactionFeedItem {
  id: string;
  type: 'chat' | 'systemLog' | 'actionCard';
  timestamp: string;
  content?: string; // For chat messages
  message?: string; // For system logs
  sender?: {
    id: string;
    name: string;
    isCurrentUser: boolean;
  };
  actionType?: string; // For action cards
  data?: any; // Additional data for action cards
}

export class TransactionalChatService {
  private prisma: PrismaClient;
  private io: Server;
  private notificationService: NotificationService;
  private chatService: ChatService;
  private transactionService: TransactionService;
  private payerNegotiationService: PayerNegotiationService;

  // 7-step transaction flow definition
  private readonly TRANSACTION_STEPS: TransactionChatStep[] = [
    {
      id: 1,
      key: 'paymentInfo',
      titleKey: 'transactionalChat.steps.paymentInfo',
      isUserAction: true,
      status: TransactionStatus.PENDING_AGREEMENT
    },
    {
      id: 2,
      key: 'negotiation',
      titleKey: 'transactionalChat.steps.negotiation',
      isUserAction: true,
      status: TransactionStatus.AWAITING_FIRST_PAYER_DESIGNATION
    },
    {
      id: 3,
      key: 'makePayment',
      titleKey: 'transactionalChat.steps.makePayment',
      isUserAction: true,
      status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT
    },
    {
      id: 4,
      key: 'confirmReceipt',
      titleKey: 'transactionalChat.steps.confirmReceipt',
      isUserAction: true,
      status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION
    },
    {
      id: 5,
      key: 'makeSecondPayment',
      titleKey: 'transactionalChat.steps.makeSecondPayment',
      isUserAction: true,
      status: TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT
    },
    {
      id: 6,
      key: 'confirmFirstPaymentReceipt',
      titleKey: 'transactionalChat.steps.confirmFirstPaymentReceipt',
      isUserAction: true,
      status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION
    },
    {
      id: 7,
      key: 'finalized',
      titleKey: 'transactionalChat.steps.finalized',
      isUserAction: false,
      status: TransactionStatus.COMPLETED
    }
  ];

  constructor(
    prisma: PrismaClient,
    io: Server,
    notificationService: NotificationService,
    chatService: ChatService,
    transactionService: TransactionService,
    payerNegotiationService: PayerNegotiationService
  ) {
    this.prisma = prisma;
    this.io = io;
    this.notificationService = notificationService;
    this.chatService = chatService;
    this.transactionService = transactionService;
    this.payerNegotiationService = payerNegotiationService;
  }

  /**
   * Get complete transaction chat details for a specific transaction
   */
  async getTransactionChatDetails(transactionId: string, userId: string): Promise<TransactionChatDetails> {
    console.log(`[TransactionalChatService] Getting chat details for transaction ${transactionId}, user ${userId}`);

    // Get transaction with related data
    const transaction = await this.prisma.transaction.findFirst({
      where: {
        id: transactionId,
        OR: [
          { currencyAProviderId: userId },
          { currencyBProviderId: userId }
        ]
      },
      include: {
        payerNegotiation: true,
        chatSession: {
          include: {
            messages: {
              include: {
                sender: {
                  select: {
                    id: true,
                    username: true,
                    reputationLevel: true
                  }
                }
              },
              orderBy: { createdAt: 'asc' }
            }
          }
        },
        currencyAProvider: {
          select: {
            id: true,
            username: true,
            reputationLevel: true
          }
        },
        currencyBProvider: {
          select: {
            id: true,
            username: true,
            reputationLevel: true
          }
        }
      }
    });

    if (!transaction) {
      throw new Error('Transaction not found or access denied');
    }

    // Determine the other user and current user role
    const isUserA = transaction.currencyAProviderId === userId;
    const otherUser = isUserA ? transaction.currencyBProvider : transaction.currencyAProvider;
    const currentUser = isUserA ? transaction.currencyAProvider : transaction.currencyBProvider;

    // Fetch payment information for both users based on PayerNegotiation selection
    let userPaymentInfo = null;
    let otherUserPaymentInfo = null;

    if (transaction.payerNegotiation) {
      // Get payment info from PayerNegotiation if available
      const userPaymentReceivingInfoId = isUserA 
        ? transaction.payerNegotiation.partyA_PaymentReceivingInfoId
        : transaction.payerNegotiation.partyB_PaymentReceivingInfoId;
      
      const otherUserPaymentReceivingInfoId = isUserA 
        ? transaction.payerNegotiation.partyB_PaymentReceivingInfoId
        : transaction.payerNegotiation.partyA_PaymentReceivingInfoId;

      // Fetch the selected payment methods
      if (userPaymentReceivingInfoId) {
        userPaymentInfo = await this.prisma.paymentReceivingInfo.findUnique({
          where: { id: userPaymentReceivingInfoId }
        });
      }
      
      if (otherUserPaymentReceivingInfoId) {
        otherUserPaymentInfo = await this.prisma.paymentReceivingInfo.findUnique({
          where: { id: otherUserPaymentReceivingInfoId }
        });
      }
    }

    // Fall back to default payment methods if not selected in negotiation
    if (!userPaymentInfo) {
      userPaymentInfo = await this.prisma.paymentReceivingInfo.findFirst({
        where: { userId, isDefaultForUser: true }
      });
    }
    
    if (!otherUserPaymentInfo) {
      otherUserPaymentInfo = await this.prisma.paymentReceivingInfo.findFirst({
        where: { userId: otherUser.id, isDefaultForUser: true }
      });
    }

    // Determine current step based on transaction status
    const currentStepIndex = this.getStepIndexFromStatus(transaction.status);
    
    // Build transaction details
    const transactionDetails = {
      amountToSend: isUserA ? transaction.amountA : transaction.amountB,
      amountToReceive: isUserA ? transaction.amountB : transaction.amountA,
      currencyFrom: isUserA ? transaction.currencyA : transaction.currencyB,
      currencyTo: isUserA ? transaction.currencyB : transaction.currencyA,
      isUserFirstPayer: transaction.agreedFirstPayerId === userId,
      otherUserPaymentDetails: otherUserPaymentInfo ? this.addValidationStatusToPaymentInfo(otherUserPaymentInfo) : null,
      userPaymentDetails: userPaymentInfo ? this.addValidationStatusToPaymentInfo(userPaymentInfo) : null
    };

    // Build feed items from chat messages and system events
    const feedItems = await this.buildFeedItems(
      transaction.chatSession?.messages || [],
      transaction,
      userId,
      currentStepIndex,
      userPaymentInfo,
      otherUserPaymentInfo
    );

    // Calculate timer if applicable
    const timer = this.calculateTimer(transaction, currentStepIndex);

    return {
      id: transaction.id,
      chatSessionId: transaction.chatSessionId,
      offerId: transaction.offerId,
      status: transaction.status,
      currencyA: transaction.currencyA,
      amountA: transaction.amountA,
      currencyAProviderId: transaction.currencyAProviderId,
      currencyB: transaction.currencyB,
      amountB: transaction.amountB,
      currencyBProviderId: transaction.currencyBProviderId,
      agreedFirstPayerId: transaction.agreedFirstPayerId,
      otherUser: {
        id: otherUser.id,
        name: otherUser.username || 'User',
        profilePic: null, // TODO: Add profile picture support
        reputation: otherUser.reputationLevel
      },
      transactionDetails,
      currentStepIndex,
      feedItems,
      timer
    };
  }

  /**
   * Send a chat message in the transaction context
   */
  async sendTransactionMessage(
    transactionId: string,
    userId: string,
    messageText: string
  ): Promise<ChatMessageReceivePayload> {
    console.log(`[TransactionalChatService] Sending message in transaction ${transactionId} from user ${userId}`);

    // Verify user has access to this transaction
    const transaction = await this.prisma.transaction.findFirst({
      where: {
        id: transactionId,
        OR: [
          { currencyAProviderId: userId },
          { currencyBProviderId: userId }
        ]
      },
      include: {
        chatSession: true
      }
    });

    if (!transaction) {
      throw new Error('Transaction not found or access denied');
    }

    // Create the chat message
    const message = await this.prisma.chatMessage.create({
      data: {
        chatSessionId: transaction.chatSessionId,
        senderId: userId,
        content: messageText,
        isSystemMessage: false,
        transactionId: transactionId
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            reputationLevel: true
          }
        }
      }
    });

    // Create socket payload
    const messagePayload: ChatMessageReceivePayload = {
      messageId: message.id,
      chatSessionId: transaction.chatSessionId,
      sender: {
        id: message.sender!.id,
        username: message.sender!.username || 'User',
        reputationLevel: message.sender!.reputationLevel
      },
      content: message.content,
      createdAt: message.createdAt.toISOString(),
      isSystemMessage: false
    };

    // Emit to both participants
    if (transaction.chatSession) {
      this.io.to(transaction.currencyAProviderId).emit(CHAT_MESSAGE_RECEIVE, messagePayload);
      this.io.to(transaction.currencyBProviderId).emit(CHAT_MESSAGE_RECEIVE, messagePayload);
    }

    return messagePayload;
  }

  /**
   * Perform a transaction action (e.g., confirm receipt, send payment)
   */
  async performTransactionAction(
    transactionId: string,
    userId: string,
    actionType: string,
    data?: any
  ): Promise<void> {
    console.log(`[TransactionalChatService] Performing action ${actionType} for transaction ${transactionId}, user ${userId}`);

    // Get transaction
    const transaction = await this.prisma.transaction.findFirst({
      where: {
        id: transactionId,
        OR: [
          { currencyAProviderId: userId },
          { currencyBProviderId: userId }
        ]
      },
      include: {
        payerNegotiation: true
      }
    });

    if (!transaction) {
      throw new Error('Transaction not found or access denied');
    }

    // Route to appropriate transaction service method based on action type
    switch (actionType) {
      case 'paymentInfo':
        // Update negotiation session with payment method info
        await this.updatePaymentReceivingInfo(transactionId, userId, data?.paymentMethodId);

        // Get username for system message
        const user = await this.prisma.user.findUnique({
          where: { id: userId },
          select: { username: true, email: true }
        });
        const username = user?.username || user?.email || `User ${userId.substring(0,6)}`;

        console.log(`🔍 [TransactionalChatService] Payment details provided - userId: ${userId}, username: ${username}`);

        await this.addSystemMessage(
          transaction.chatSessionId,
          'transactionalChat.systemLogs.paymentDetailsProvided',
          transactionId,
          { username }
        );
        break;

      case 'negotiation':
        // Use existing payer designation logic
        if (data?.designatedPayerId) {
          await this.transactionService.designateFirstPayer(transactionId, userId, data.designatedPayerId);
        }
        break;

      case 'confirmReceipt':
        // Use existing confirm receipt logic
        await this.transactionService.confirmReceipt(transactionId, userId);
        break;

      case 'confirmFirstPayerPayment':
        // Use existing confirm receipt logic for first payer payment
        await this.transactionService.confirmReceipt(transactionId, userId);
        
        await this.addSystemMessage(
          transaction.chatSessionId,
          'transactionalChat.systemLogs.firstPaymentConfirmed',
          transactionId
        );
        break;

      case 'declareFirstPayerPayment':
        // Use existing declare payment logic for first payer
        // Note: transactionService.declarePayment already creates the comprehensive system message
        // with username, amount, and next steps information
        await this.transactionService.declarePayment(transactionId, userId, data?.trackingNumber, data?.notes);
        break;

      case 'secondPayment':
        // Use existing declare payment logic
        await this.transactionService.declarePayment(transactionId, userId, data?.trackingNumber, data?.notes);
        break;

      case 'declareSecondPayerPayment':
        // Use existing declare payment logic for second payer
        // Note: transactionService.declarePayment already creates the comprehensive system message
        // with username, amount, and next steps information
        await this.transactionService.declarePayment(transactionId, userId, data?.trackingNumber, data?.notes);
        break;

      case 'rateExperience':
        // TODO: Implement rating system
        await this.addSystemMessage(
          transaction.chatSessionId,
          'transactionalChat.systemLogs.ratingSubmitted',
          transactionId
        );
        break;

      default:
        throw new Error(`Unknown action type: ${actionType}`);
    }
  }

  /**
   * Add a system message to the transaction chat
   */
  private async addSystemMessage(
    chatSessionId: string,
    messageKey: string,
    transactionId: string,
    params?: any
  ): Promise<void> {
    console.log(`🔍 [TransactionalChatService] addSystemMessage called:`, {
      chatSessionId,
      messageKey,
      transactionId,
      params: JSON.stringify(params, null, 2)
    });

    // Create system message in database
    const message = await this.prisma.chatMessage.create({
      data: {
        chatSessionId,
        content: messageKey, // Store the translation key
        isSystemMessage: true,
        senderId: null,
        transactionId
      }
    });

    console.log(`🔍 [TransactionalChatService] Created database message:`, {
      messageId: message.id,
      content: message.content,
      createdAt: message.createdAt.toISOString()
    });

    // Create socket payload
    const systemMessagePayload: SystemMessagePayload = {
      messageId: message.id,
      chatSessionId,
      content: messageKey,
      createdAt: message.createdAt.toISOString(),
      isSystemMessage: true,
      transactionId,
      data: params // Include translation parameters for rich contextual messages
    };

    console.log(`🔍 [TransactionalChatService] About to emit SystemMessagePayload:`, {
      payload: JSON.stringify(systemMessagePayload, null, 2)
    });

    // Get chat session participants
    const chatSession = await this.prisma.chatSession.findUnique({
      where: { id: chatSessionId },
      select: { userOneId: true, userTwoId: true }
    });

    if (chatSession) {
      // Emit to both participants
      this.io.to(chatSession.userOneId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      if (chatSession.userTwoId !== chatSession.userOneId) {
        this.io.to(chatSession.userTwoId).emit(SYSTEM_MESSAGE_RECEIVE, systemMessagePayload);
      }
    }
  }

  /**
   * Get step index from transaction status
   */
  private getStepIndexFromStatus(status: TransactionStatus): number {
    const stepIndex = this.TRANSACTION_STEPS.findIndex(step => step.status === status);
    return stepIndex >= 0 ? stepIndex : 0;
  }

  /**
   * Format amount for display in system messages
   */
  private formatAmount(amount: number, currency: string): string {
    if (currency === 'IRR' && amount >= 1000000) {
      // Format IRR amounts in millions for better readability
      const millions = amount / 1000000;
      return `${millions.toFixed(1)}M ${currency}`;
    }
    return `${amount.toLocaleString()} ${currency}`;
  }

  /**
   * Reconstruct contextual data for historical system messages
   */
  private async reconstructSystemMessageData(message: any, transaction: any): Promise<any> {
    const messageContent = message.content;

    console.log(`🔍 [TransactionalChatService] Reconstructing data for message: ${messageContent}`);

    // Get user information
    const [userA, userB] = await Promise.all([
      this.prisma.user.findUnique({
        where: { id: transaction.currencyAProviderId },
        select: { username: true, email: true }
      }),
      this.prisma.user.findUnique({
        where: { id: transaction.currencyBProviderId },
        select: { username: true, email: true }
      })
    ]);

    const userAName = userA?.username || userA?.email || 'User A';
    const userBName = userB?.username || userB?.email || 'User B';
    const firstPayerName = transaction.agreedFirstPayerId === transaction.currencyAProviderId ? userAName : userBName;

    // Reconstruct data based on message type
    switch (messageContent) {
      case 'systemMessages.payment.declared':
        // For historical messages, use the data that was originally saved with the message.
        // The `data` field of the message should contain username, otherUser, and amount.
        return message.data || {};

      case 'systemMessages.proposal.agreed':
        return message.data || {};

      case 'systemMessages.proposal.bothAgreed':
      case 'systemMessages.proposal.bothAgreedSystemDesignated':
        // For historical messages, use the data that was originally saved with the message.
        // The `data` field of the message should contain firstPayer and dueDate.
        return message.data || {};

      case 'systemMessages.payment.confirmedFirst':
      case 'systemMessages.payment.confirmedSecond':
        // For historical messages, use the data that was originally saved with the message.
        // The `data` field of the message should contain username, payerUser, receiverUser, amount, and dueDate.
        return message.data || {};

      case 'transactionalChat.systemLogs.paymentDetailsProvided':
        // For historical messages, use the data that was originally saved with the message.
        // The `data` field of the message should contain the username.
        return message.data || {};

      case 'transactionalChat.systemLogs.readyToNegotiate':
        return {}; // No specific data needed

      case 'transactionalChat.systemLogs.firstPaymentDeclared':
        return message.data || {};

      case 'transactionalChat.systemLogs.secondPaymentDeclared':
        return message.data || {};

      case 'transactionalChat.systemLogs.firstPaymentConfirmed':
        return message.data || {};

      case 'transactionalChat.systemLogs.agreementReached':
        return message.data || {};

      case 'systemMessages.proposal.from':
        return message.data || {};

      case 'systemMessages.transaction.complete':
        return message.data || {};

      case 'systemMessages.transaction.cancelled':
        return message.data || {};

      case 'systemMessages.transaction.disputed':
        return message.data || {};

      case 'transactionalChat.systemLogs.ratingSubmitted':
        return message.data || {};

      default:
        console.log(`🔍 [TransactionalChatService] No specific reconstruction logic for message: ${messageContent}. Returning original data if available.`);
        return message.data || {};
    }
  }

  /**
   * Build feed items from chat messages and transaction state
   */
  private async buildFeedItems(
    messages: (ChatMessage & {
      sender: {
        id: string;
        username: string | null;
        reputationLevel: number;
      } | null;
    })[],
    transaction: any,
    userId: string,
    currentStepIndex: number,
    userPaymentInfo?: any,
    otherUserPaymentInfo?: any
  ): Promise<TransactionFeedItem[]> {
    const feedItems: TransactionFeedItem[] = [];

    // Convert chat messages to feed items
    for (const message of messages) {
      if (message.isSystemMessage) {
        console.log(`🔍 [TransactionalChatService] Processing historical system message:`, {
          messageId: message.id,
          content: message.content,
          createdAt: message.createdAt.toISOString()
        });

        // For historical messages, we need to reconstruct the contextual data
        // since it wasn't stored in the database originally
        const reconstructedData = await this.reconstructSystemMessageData(message, transaction);

        console.log(`🔍 [TransactionalChatService] Reconstructed data for historical message:`, {
          messageId: message.id,
          reconstructedData: JSON.stringify(reconstructedData, null, 2)
        });

        feedItems.push({
          id: message.id,
          type: 'systemLog',
          timestamp: message.createdAt.toISOString(),
          message: message.content,
          data: reconstructedData // Include reconstructed contextual data
        });
      } else if (message.sender) {
        feedItems.push({
          id: message.id,
          type: 'chat',
          timestamp: message.createdAt.toISOString(),
          content: message.content,
          sender: {
            id: message.sender.id,
            name: message.sender.username || 'User',
            isCurrentUser: message.sender.id === userId
          }
        });
      }
    }

    // Add action cards based on current step and user turn
    const currentStep = this.TRANSACTION_STEPS[currentStepIndex];
    if (currentStep?.isUserAction) {
      const isUsersTurn = this.isUsersTurn(transaction, userId, currentStep);
      
      if (isUsersTurn) {
        // User's turn to act - show active action card
        const actionCardData = await this.getActionCardData(transaction, currentStep, userId, userPaymentInfo, otherUserPaymentInfo);
        
        feedItems.push({
          id: `action-${currentStep.key}`,
          type: 'actionCard',
          timestamp: new Date().toISOString(),
          actionType: currentStep.key,
          data: actionCardData
        });
      } else {
        // Check if user should see a waiting/status card
        const shouldShowWaitingCard = this.shouldShowWaitingCard(transaction, userId, currentStep);
        
        if (shouldShowWaitingCard) {
          const waitingCardData = await this.getWaitingCardData(transaction, currentStep, userId, userPaymentInfo, otherUserPaymentInfo);
          
          feedItems.push({
            id: `waiting-${currentStep.key}`,
            type: 'actionCard',
            timestamp: new Date().toISOString(),
            actionType: `waiting_${currentStep.key}`,
            data: waitingCardData
          });
        }
      }
    }

    // Sort by timestamp
    feedItems.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    return feedItems;
  }

  /**
   * Check if a user has provided their payment info for the transaction
   */
  private hasUserProvidedPaymentInfo(transaction: any, userId: string): boolean {
    if (!transaction.payerNegotiation) return false;
    
    const isPartyA = transaction.currencyAProviderId === userId;
    const isPartyB = transaction.currencyBProviderId === userId;
    
    if (isPartyA) {
      return transaction.payerNegotiation.partyA_receivingInfoStatus === 'PROVIDED';
    } else if (isPartyB) {
      return transaction.payerNegotiation.partyB_receivingInfoStatus === 'PROVIDED';
    }
    
    return false;
  }

  /**
   * Determine if it's the user's turn to act
   */
  private isUsersTurn(transaction: any, userId: string, step: TransactionChatStep): boolean {
    if (!step.isUserAction) return false;

    switch (step.key) {
      case 'paymentInfo':
        // INDEPENDENT: Each user can provide payment info individually
        // Check if this specific user has already provided their info
        return this.hasUserProvidedPaymentInfo(transaction, userId) === false;

      case 'negotiation':
        // Both users need to negotiate (but only if both have provided payment info)
        return true;

      case 'makePayment':
        // User needs to pay if they are the first payer
        return transaction.agreedFirstPayerId === userId;

      case 'confirmReceipt':
        // User needs to confirm if they are the second payer (receiver of first payment)
        return transaction.agreedFirstPayerId !== userId;

      case 'makeSecondPayment':
        // User needs to pay if they are the second payer
        return transaction.agreedFirstPayerId !== userId;

      case 'confirmFirstPaymentReceipt':
        // User needs to confirm if they are the first payer (receiver of second payment)
        return transaction.agreedFirstPayerId === userId;

      default:
        return false;
    }
  }

  /**
   * Determine if user should see a waiting/status card when it's not their turn
   */
  private shouldShowWaitingCard(transaction: any, userId: string, step: TransactionChatStep): boolean {
    if (!step.isUserAction) return false;

    switch (step.key) {
      case 'paymentInfo':
        // Don't show waiting card for payment info - each user acts independently
        return false;

      case 'negotiation':
        // Don't show waiting card for negotiation - both users can participate
        return false;

      case 'makePayment':
        // Second payer sees waiting card while first payer makes payment
        return transaction.agreedFirstPayerId !== userId;

      case 'confirmReceipt':
        // First payer sees waiting card while second payer confirms receipt
        return transaction.agreedFirstPayerId === userId;

      case 'makeSecondPayment':
        // First payer sees waiting card while second payer makes payment
        return transaction.agreedFirstPayerId === userId;

      case 'confirmFirstPaymentReceipt':
        // Second payer sees waiting card while first payer confirms receipt
        return transaction.agreedFirstPayerId !== userId;

      default:
        return false;
    }
  }

  /**
   * Get action card specific data
   */
  private async getActionCardData(transaction: any, step: TransactionChatStep, userId: string, userPaymentInfo?: any, otherUserPaymentInfo?: any): Promise<any> {
    const isUserA = transaction.currencyAProviderId === userId;
    
    switch (step.key) {
      case 'paymentInfo':
        // For payment info, user needs payment methods for the currency they will RECEIVE
        // If isUserA: they provide currencyA but RECEIVE currencyB
        // If isUserB: they provide currencyB but RECEIVE currencyA
        const receivingCurrency = isUserA ? transaction.currencyB : transaction.currencyA;
        const userCurrencyPaymentMethods = await this.getUserPaymentMethodsByCurrency(userId, receivingCurrency);
        console.log('🔍 [Backend] getActionCardData for paymentInfo:', {
          userId,
          isUserA,
          providingCurrency: isUserA ? transaction.currencyA : transaction.currencyB,
          receivingCurrency,
          methodCount: userCurrencyPaymentMethods.length,
          methods: userCurrencyPaymentMethods.map(m => ({ id: m.id, currency: m.currency, bank: m.bankName }))
        });
        return {
          // Enhanced payment info data for smart inline editing
          userCurrentPaymentInfo: userPaymentInfo ? this.addValidationStatusToPaymentInfo(userPaymentInfo) : null,
          userAllPaymentMethods: userCurrencyPaymentMethods,
          canEditInline: true,
          currency: receivingCurrency,
          amount: isUserA ? transaction.amountB : transaction.amountA
        };

      case 'negotiation':
        return {
          // Enhanced negotiation data with visual recommendations
          recommendedFirstPayer: this.calculateRecommendedFirstPayer(transaction, userId),
          userRole: isUserA ? 'currencyAProvider' : 'currencyBProvider',
          otherUserInfo: {
            name: isUserA ? 'Currency B Provider' : 'Currency A Provider', // TODO: Get actual username
            reputation: 0 // TODO: Get actual reputation
          },
          negotiationContext: {
            userSends: { amount: isUserA ? transaction.amountA : transaction.amountB, currency: isUserA ? transaction.currencyA : transaction.currencyB },
            userReceives: { amount: isUserA ? transaction.amountB : transaction.amountA, currency: isUserA ? transaction.currencyB : transaction.currencyA }
          }
        };

      case 'makePayment':
        return {
          amount: isUserA ? transaction.amountA : transaction.amountB,
          currency: isUserA ? transaction.currencyA : transaction.currencyB,
          otherUserPaymentDetails: otherUserPaymentInfo ? this.addValidationStatusToPaymentInfo(otherUserPaymentInfo) : null,
          paymentInstructions: this.generatePaymentInstructions(otherUserPaymentInfo),
          canDeclarePayment: true,
          trackingNumberRequired: true
        };

      case 'confirmReceipt':
        return {
          amount: isUserA ? transaction.amountB : transaction.amountA,
          currency: isUserA ? transaction.currencyB : transaction.currencyA,
          userPaymentDetails: userPaymentInfo ? this.addValidationStatusToPaymentInfo(userPaymentInfo) : null,
          expectedPaymentWindow: this.calculatePaymentWindow(transaction),
          canProvideTrackingInfo: true,
          // Include payment declaration details from first payer
          referenceNumber: transaction.paymentTrackingNumberPayer1,
          notes: transaction.paymentNotesPayer1
        };

      case 'makeSecondPayment':
        // Second payer makes their payment (sends their own currency)
        // Since only second payer can do this action, they send their natural currency
        return {
          amount: isUserA ? transaction.amountA : transaction.amountB,
          currency: isUserA ? transaction.currencyA : transaction.currencyB,
          otherUserPaymentDetails: otherUserPaymentInfo ? this.addValidationStatusToPaymentInfo(otherUserPaymentInfo) : null,
          paymentInstructions: this.generatePaymentInstructions(otherUserPaymentInfo),
          canDeclarePayment: true,
          trackingNumberRequired: true
        };

      case 'confirmFirstPaymentReceipt':
        // First payer confirms receiving the second payer's payment
        // The first payer receives the OTHER currency (what the second payer sends)
        return {
          amount: isUserA ? transaction.amountB : transaction.amountA,
          currency: isUserA ? transaction.currencyB : transaction.currencyA,
          userPaymentDetails: userPaymentInfo ? this.addValidationStatusToPaymentInfo(userPaymentInfo) : null,
          expectedPaymentWindow: this.calculatePaymentWindow(transaction),
          canProvideTrackingInfo: true,
          // Include payment declaration details from second payer
          referenceNumber: transaction.paymentTrackingNumberPayer2,
          notes: transaction.paymentNotesPayer2
        };

      default:
        return {};
    }
  }

  /**
   * Get waiting card specific data for users who are waiting for others to act
   */
  private async getWaitingCardData(transaction: any, step: TransactionChatStep, userId: string, userPaymentInfo?: any, otherUserPaymentInfo?: any): Promise<any> {
    const isUserA = transaction.currencyAProviderId === userId;
    
    switch (step.key) {
      case 'makePayment':
        // Second payer waiting for first payer to make payment
        return {
          waitingFor: 'firstPayerPayment',
          amount: isUserA ? transaction.amountB : transaction.amountA,
          currency: isUserA ? transaction.currencyB : transaction.currencyA,
          otherUserRole: 'firstPayer',
          statusMessage: 'Waiting for other party to make their payment',
          isWaitingCard: true
        };

      case 'confirmReceipt':
        // First payer waiting for second payer to confirm receipt
        return {
          waitingFor: 'secondPayerConfirmation',
          amount: isUserA ? transaction.amountA : transaction.amountB,
          currency: isUserA ? transaction.currencyA : transaction.currencyB,
          otherUserRole: 'secondPayer',
          statusMessage: 'Waiting for other party to confirm receipt of your payment',
          paymentSent: true,
          trackingNumber: transaction.paymentTrackingNumberPayer1,
          isWaitingCard: true
        };

      case 'makeSecondPayment':
        // First payer waiting for second payer to make their payment
        return {
          waitingFor: 'secondPayerPayment',
          amount: isUserA ? transaction.amountB : transaction.amountA,
          currency: isUserA ? transaction.currencyB : transaction.currencyA,
          otherUserRole: 'secondPayer',
          statusMessage: 'Waiting for other party to make their payment',
          expectedPaymentWindow: this.calculatePaymentWindow(transaction),
          isWaitingCard: true
        };

      case 'confirmFirstPaymentReceipt':
        // Second payer waiting for first payer to confirm receipt of second payment
        return {
          waitingFor: 'firstPayerConfirmation',
          amount: isUserA ? transaction.amountA : transaction.amountB,
          currency: isUserA ? transaction.currencyA : transaction.currencyB,
          otherUserRole: 'firstPayer',
          statusMessage: 'Waiting for other party to confirm receipt of your payment',
          paymentSent: true,
          trackingNumber: transaction.paymentTrackingNumberPayer2,
          isWaitingCard: true
        };

      default:
        return {
          isWaitingCard: true,
          statusMessage: 'Waiting for other party to complete their action'
        };
    }
  }

  /**
   * Get all payment methods for a user (for inline selection)
   */
  private async getUserPaymentMethods(userId: string): Promise<any[]> {
    const paymentMethods = await this.prisma.paymentReceivingInfo.findMany({
      where: { userId },
      orderBy: [
        { isDefaultForUser: 'desc' },
        { createdAt: 'desc' }
      ]
    });
    
    return paymentMethods.map(method => this.addValidationStatusToPaymentInfo(method));
  }

  private async getUserPaymentMethodsByCurrency(userId: string, currency: string): Promise<any[]> {
    const paymentMethods = await this.prisma.paymentReceivingInfo.findMany({
      where: { 
        userId,
        currency,
        isActive: true
      },
      orderBy: [
        { isDefaultForUser: 'desc' },
        { createdAt: 'desc' }
      ]
    });
    
    return paymentMethods.map(method => this.addValidationStatusToPaymentInfo(method));
  }

  /**
   * Calculate recommended first payer based on reputation and transaction context
   */
  private calculateRecommendedFirstPayer(transaction: any, userId: string): string {
    // Simple logic for now - can be enhanced with reputation, amounts, etc.
    // For now, recommend the user with lower amount pays first (safer)
    const isUserA = transaction.currencyAProviderId === userId;
    const userAmount = isUserA ? transaction.amountA : transaction.amountB;
    const otherAmount = isUserA ? transaction.amountB : transaction.amountA;
    
    if (userAmount < otherAmount) {
      return userId; // User should pay first (lower risk)
    } else {
      return isUserA ? transaction.currencyBProviderId : transaction.currencyAProviderId;
    }
  }

  /**
   * Calculate payment time window
   */
  private calculatePaymentWindow(transaction: any): { expectedBy: Date; isUrgent: boolean } {
    const now = new Date();
    const expectedBy = new Date(now.getTime() + (24 * 60 * 60 * 1000)); // 24 hours from now
    const isUrgent = false; // TODO: Add urgency logic
    
    return { expectedBy, isUrgent };
  }

  /**
   * Generate step-by-step payment instructions
   */
  private generatePaymentInstructions(paymentInfo: any): string[] {
    if (!paymentInfo) return [];
    
    const instructions = [
      `Transfer to: ${paymentInfo.bankName}`,
      `Account: ${paymentInfo.accountNumber}`,
      `Name: ${paymentInfo.accountHolderName}`
    ];
    
    if (paymentInfo.iban) {
      instructions.push(`IBAN: ${paymentInfo.iban}`);
    }
    
    if (paymentInfo.swiftCode) {
      instructions.push(`SWIFT: ${paymentInfo.swiftCode}`);
    }
    
    return instructions;
  }

  /**
   * Calculate timer for time-sensitive steps
   */
  private calculateTimer(transaction: any, currentStepIndex: number): { isActive: boolean; remainingSeconds: number; dueDate?: string } | undefined {
    const step = this.TRANSACTION_STEPS[currentStepIndex];
    
    // Timer is active for payment-related steps
    if (step?.key === 'confirmReceipt' || step?.key === 'waitingSecondPayment') {
      let dueDate: Date | null = null;
      
      if (step.key === 'confirmReceipt' && transaction.paymentExpectedByPayer1) {
        dueDate = new Date(transaction.paymentExpectedByPayer1);
      } else if (step.key === 'waitingSecondPayment' && transaction.paymentExpectedByPayer2) {
        dueDate = new Date(transaction.paymentExpectedByPayer2);
      }
      
      if (dueDate) {
        const now = new Date();
        const remainingMs = dueDate.getTime() - now.getTime();
        const remainingSeconds = Math.max(0, Math.floor(remainingMs / 1000));
        
        return {
          isActive: remainingSeconds > 0,
          remainingSeconds,
          dueDate: dueDate.toISOString()
        };
      }
    }
    
    return undefined;
  }

  /**
   * Update payment receiving info for a user in the transaction
   */
  private async updatePaymentReceivingInfo(
    transactionId: string,
    userId: string,
    paymentMethodId?: string
  ): Promise<void> {
    console.log(`[TransactionalChatService] Updating payment receiving info for transaction ${transactionId}, user ${userId}, paymentMethodId ${paymentMethodId}`);

    // Get the transaction and negotiation session
    const transaction = await this.prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        payerNegotiation: true
      }
    });

    if (!transaction) {
      throw new Error('Transaction not found');
    }

    if (!transaction.payerNegotiation) {
      // Initialize the negotiation if it doesn't exist
      console.log(`[TransactionalChatService] PayerNegotiation not found for transaction ${transactionId}, initializing...`);
      await this.payerNegotiationService.initializeNegotiation(transactionId);
      
      // Re-fetch the transaction with the newly created negotiation
      const updatedTransaction = await this.prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          payerNegotiation: true
        }
      });
      
      if (!updatedTransaction?.payerNegotiation) {
        throw new Error('Failed to initialize payer negotiation for transaction');
      }
      
      transaction.payerNegotiation = updatedTransaction.payerNegotiation;
    }

    const payerNegotiation = transaction.payerNegotiation;
    
    // Determine if user is party A or party B
    const isPartyA = transaction.currencyAProviderId === userId;
    const isPartyB = transaction.currencyBProviderId === userId;
    
    if (!isPartyA && !isPartyB) {
      throw new Error('User is not a participant in this transaction');
    }

    // Update the negotiation session with payment method info
    const updateData: any = {};
    
    if (isPartyA) {
      updateData.partyA_receivingInfoStatus = 'PROVIDED';
      if (paymentMethodId) {
        updateData.partyA_PaymentReceivingInfoId = paymentMethodId;
      }
    } else {
      updateData.partyB_receivingInfoStatus = 'PROVIDED';
      if (paymentMethodId) {
        updateData.partyB_PaymentReceivingInfoId = paymentMethodId;
      }
    }

    // Check if both parties have now provided their info
    const currentPartyAStatus = isPartyA ? 'PROVIDED' : payerNegotiation.partyA_receivingInfoStatus;
    const currentPartyBStatus = isPartyB ? 'PROVIDED' : payerNegotiation.partyB_receivingInfoStatus;
    
    // Update negotiation status based on current state
    if (currentPartyAStatus === 'PROVIDED' && currentPartyBStatus === 'PROVIDED') {
      updateData.negotiationStatus = 'READY_TO_NEGOTIATE';
    } else if (currentPartyAStatus === 'PROVIDED' && currentPartyBStatus === 'PENDING_INPUT') {
      updateData.negotiationStatus = 'AWAITING_PARTY_B_RECEIVING_INFO';
    } else if (currentPartyAStatus === 'PENDING_INPUT' && currentPartyBStatus === 'PROVIDED') {
      updateData.negotiationStatus = 'AWAITING_PARTY_A_RECEIVING_INFO';
    }

    // Update the negotiation session
    await this.prisma.payerNegotiation.update({
      where: { transactionId },
      data: updateData
    });

    // If both parties have provided info, move to negotiation step
    if (currentPartyAStatus === 'PROVIDED' && currentPartyBStatus === 'PROVIDED') {
      // Update transaction status to enable negotiation
      const updatedTransaction = await this.prisma.transaction.update({
        where: { id: transactionId },
        data: { 
          status: 'AWAITING_FIRST_PAYER_DESIGNATION',
          updatedAt: new Date()
        }
      });

      // Emit transaction status update to both participants
      await this.emitTransactionStatusUpdate(updatedTransaction);

      // Add a small delay to ensure proper chronological order of system messages
      await new Promise(resolve => setTimeout(resolve, 100));

      // Add system message that both parties are ready to negotiate
      await this.addSystemMessage(
        transaction.chatSessionId,
        'transactionalChat.systemLogs.readyToNegotiate',
        transactionId,
        {} // No specific data needed for this message
      );
    } else {
      // Even if only one user provided info, emit update so their UI progresses
      // The transaction status stays the same, but UI can show individual progress
      await this.emitTransactionStatusUpdate(transaction);
    }

    console.log(`[TransactionalChatService] Payment receiving info updated for user ${userId} in transaction ${transactionId}`);
  }

  /**
   * Emit transaction status update to participants
   */
  private async emitTransactionStatusUpdate(transaction: any): Promise<void> {
    // Get chat session participants and user details
    const chatSession = await this.prisma.chatSession.findUnique({
      where: { id: transaction.chatSessionId },
      select: { userOneId: true, userTwoId: true }
    });

    if (chatSession) {
      // Get user details for the transaction
      const [currencyAProvider, currencyBProvider] = await Promise.all([
        this.prisma.user.findUnique({
          where: { id: transaction.currencyAProviderId },
          select: { username: true }
        }),
        this.prisma.user.findUnique({
          where: { id: transaction.currencyBProviderId },
          select: { username: true }
        })
      ]);

      const payload: TransactionStatusUpdatePayload = {
        offerId: transaction.offerId,
        chatSessionId: transaction.chatSessionId,
        transactionId: transaction.id,
        status: transaction.status,
        currencyA: transaction.currencyA,
        amountA: transaction.amountA,
        currencyAProviderId: transaction.currencyAProviderId,
        currencyAProviderUsername: currencyAProvider?.username || null,
        currencyB: transaction.currencyB,
        amountB: transaction.amountB,
        currencyBProviderId: transaction.currencyBProviderId,
        currencyBProviderUsername: currencyBProvider?.username || null,
        agreedFirstPayerId: transaction.agreedFirstPayerId,
        termsAgreementTimestampPayer1: transaction.termsAgreementTimestampPayer1?.toISOString() || null,
        termsAgreementTimestampPayer2: transaction.termsAgreementTimestampPayer2?.toISOString() || null,
        paymentExpectedByPayer1: transaction.paymentExpectedByPayer1?.toISOString() || null,
        paymentDeclaredAtPayer1: transaction.paymentDeclaredAtPayer1?.toISOString() || null,
        paymentTrackingNumberPayer1: transaction.paymentTrackingNumberPayer1,
        firstPaymentConfirmedByPayer2At: transaction.firstPaymentConfirmedByPayer2At?.toISOString() || null,
        paymentExpectedByPayer2: transaction.paymentExpectedByPayer2?.toISOString() || null,
        paymentDeclaredAtPayer2: transaction.paymentDeclaredAtPayer2?.toISOString() || null,
        paymentTrackingNumberPayer2: transaction.paymentTrackingNumberPayer2,
        secondPaymentConfirmedByPayer1At: transaction.secondPaymentConfirmedByPayer1At?.toISOString() || null,
        cancellationReason: transaction.cancellationReason,
        disputeReason: transaction.disputeReason,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString()
      };

      // Emit to both participants
      this.io.to(chatSession.userOneId).emit(TRANSACTION_STATUS_UPDATED, payload);
      if (chatSession.userTwoId !== chatSession.userOneId) {
        this.io.to(chatSession.userTwoId).emit(TRANSACTION_STATUS_UPDATED, payload);
      }

      console.log(`[TransactionalChatService] Emitted transaction status update for transaction ${transaction.id}`);
    }
  }

  /**
   * Add validation status to payment info
   * Similar to PaymentMethodService but for raw PaymentReceivingInfo objects
   */
  private addValidationStatusToPaymentInfo(paymentInfo: any): any {
    const missingFields: string[] = [];
    
    // Check required fields
    if (!paymentInfo.bankName) missingFields.push('bankName');
    if (!paymentInfo.accountNumber) missingFields.push('accountNumber');
    if (!paymentInfo.accountHolderName) missingFields.push('accountHolderName');

    const validationStatus = missingFields.length === 0 ? 'complete' : 'pending';

    return {
      id: paymentInfo.id,
      currency: paymentInfo.currency,
      paymentMethodType: paymentInfo.paymentMethodType,
      bankName: paymentInfo.bankName,
      accountNumber: paymentInfo.accountNumber,
      accountHolderName: paymentInfo.accountHolderName,
      iban: paymentInfo.iban,
      swiftCode: paymentInfo.swiftCode,
      routingNumber: paymentInfo.routingNumber,
      sortCode: paymentInfo.sortCode,
      bsb: paymentInfo.bsb,
      notes: paymentInfo.notes,
      isDefaultForUser: paymentInfo.isDefaultForUser,
      isActive: paymentInfo.isActive,
      validationStatus,
      isComplete: validationStatus === 'complete',
      missingFields
    };
  }
}
