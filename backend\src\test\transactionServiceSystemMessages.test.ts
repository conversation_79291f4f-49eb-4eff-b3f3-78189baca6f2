
import { TransactionService } from '../../src/services/transactionService';
import { PrismaClient, TransactionStatus, Prisma } from '@prisma/client';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Server } from 'socket.io';
import { NotificationService } from '../../src/services/notificationService';
import { ChatService } from '../../src/services/chatService';
import { SYSTEM_MESSAGE_RECEIVE } from '../../src/types/socketEvents';

// Mock PrismaClient
const prismaMock = mockDeep<PrismaClient>();

// Mock dependencies for TransactionService
const ioMock: DeepMockProxy<Server> = mockDeep<Server>();
const notificationServiceMock: DeepMockProxy<NotificationService> = mockDeep<NotificationService>();
const chatServiceMock: DeepMockProxy<ChatService> = mockDeep<ChatService>();

// Create an instance of TransactionService with mocked dependencies
let transactionService: TransactionService;

// Mock user data
const userH = {
  id: 'user-h-id',
  username: 'h',
  email: '<EMAIL>',
  reputationLevel: 5,
  reputationScore: 100,
};

const userH2 = {
  id: 'user-h2-id',
  username: 'h2',
  email: '<EMAIL>',
  reputationLevel: 3,
  reputationScore: 80,
};

// Mock transaction data
const mockTransaction = {
  id: 'transaction-id',
  chatSessionId: 'chat-session-id',
  offerId: 'offer-id',
  status: TransactionStatus.AWAITING_FIRST_PAYER_PAYMENT,
  currencyA: 'CAD',
  amountA: 500,
  currencyAProviderId: userH.id, // h provides CAD
  currencyB: 'IRR',
  amountB: 28500000,
  currencyBProviderId: userH2.id, // h2 provides IRR
  agreedFirstPayerId: userH2.id, // h2 is the first payer
  firstPayerDesignationTimestamp: new Date(),
  paymentExpectedByPayer1: new Date(Date.now() + 3600000), // 1 hour from now
  paymentDeclaredAtPayer1: null,
  paymentTrackingNumberPayer1: null,
  paymentNotesPayer1: null,
  firstPaymentConfirmedByPayer2At: null,
  paymentExpectedByPayer2: null,
  paymentDeclaredAtPayer2: null,
  paymentTrackingNumberPayer2: null,
  paymentNotesPayer2: null,
  secondPaymentConfirmedByPayer1At: null,
  cancellationReason: null,
  cancelledByUserId: null,
  disputeReason: null,
  disputedByUserId: null,
  disputeResolutionNotes: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Helper to capture system message payload
const getSystemMessagePayload = () => {
  const emitCall = ioMock.to.mock.calls.find((call: any[]) => call[0] === userH.id || call[0] === userH2.id);
  if (emitCall && emitCall[1] === SYSTEM_MESSAGE_RECEIVE) {
    return emitCall[2];
  }
  return null;
};

async function runTests() {
  // Setup
  jest.mock('../../src/utils/database', () => ({
    getPrismaClient: jest.fn(() => prismaMock),
  }));
  transactionService = new TransactionService(ioMock, notificationServiceMock, chatServiceMock);

  // Test: should emit correct system message data when first payer (h2) declares payment
  let updatedTransaction = {
    ...mockTransaction,
    status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION,
    paymentDeclaredAtPayer1: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.update.mockResolvedValue(updatedTransaction as any);

  await transactionService.declarePayment(mockTransaction.id, userH2.id, 'tracking123', 'notes');

  let systemMessagePayload = getSystemMessagePayload();
  console.log('Test 1: First payer declares payment');
  console.log('Expected:', {
    username: userH2.username,
    otherUser: userH.username,
    amount: '28.5M IRR',
  });
  console.log('Received:', systemMessagePayload.data);
  console.assert(systemMessagePayload.content === 'systemMessages.payment.declared', 'Test 1 Failed: Incorrect content');
  console.assert(systemMessagePayload.data.username === userH2.username, 'Test 1 Failed: Incorrect username');
  console.assert(systemMessagePayload.data.otherUser === userH.username, 'Test 1 Failed: Incorrect otherUser');
  console.assert(systemMessagePayload.data.amount === '28.5M IRR', 'Test 1 Failed: Incorrect amount');

  // Test: should emit correct system message data when second payer (h) declares payment
  // Simulate transaction state after first payment declared and confirmed
  let transactionAfterFirstPayment = {
    ...mockTransaction,
    status: TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT,
    paymentDeclaredAtPayer1: new Date(),
    firstPaymentConfirmedByPayer2At: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.findUnique.mockResolvedValue(transactionAfterFirstPayment as any);

  updatedTransaction = {
    ...transactionAfterFirstPayment,
    status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION,
    paymentDeclaredAtPayer2: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.update.mockResolvedValue(updatedTransaction as any);

  await transactionService.declarePayment(mockTransaction.id, userH.id, 'tracking456', 'notes');

  systemMessagePayload = getSystemMessagePayload();
  console.log('Test 2: Second payer declares payment');
  console.log('Expected:', {
    username: userH.username,
    otherUser: userH2.username,
    amount: '500 CAD',
  });
  console.log('Received:', systemMessagePayload.data);
  console.assert(systemMessagePayload.content === 'systemMessages.payment.declared', 'Test 2 Failed: Incorrect content');
  console.assert(systemMessagePayload.data.username === userH.username, 'Test 2 Failed: Incorrect username');
  console.assert(systemMessagePayload.data.otherUser === userH2.username, 'Test 2 Failed: Incorrect otherUser');
  console.assert(systemMessagePayload.data.amount === '500 CAD', 'Test 2 Failed: Incorrect amount');

  // Test: should emit correct system message data when receiver (h) confirms first payment
  // Simulate transaction state after first payment declared
  let transactionAfterFirstPaymentDeclared = {
    ...mockTransaction,
    status: TransactionStatus.AWAITING_SECOND_PAYER_CONFIRMATION,
    paymentDeclaredAtPayer1: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.findUnique.mockResolvedValue(transactionAfterFirstPaymentDeclared as any);

  updatedTransaction = {
    ...transactionAfterFirstPaymentDeclared,
    status: TransactionStatus.AWAITING_SECOND_PAYER_PAYMENT,
    firstPaymentConfirmedByPayer2At: new Date(),
    paymentExpectedByPayer2: new Date(Date.now() + 3600000),
  } as Prisma.Transaction;
  prismaMock.transaction.update.mockResolvedValue(updatedTransaction as any);

  await transactionService.confirmReceipt(mockTransaction.id, userH.id); // h confirms h2's payment

  systemMessagePayload = getSystemMessagePayload();
  console.log('Test 3: Receiver confirms first payment');
  console.log('Expected:', {
    username: userH.username,
    payerUser: userH2.username,
    receiverUser: userH.username,
    amount: '28.5M IRR',
    dueDate: expect.any(String),
  });
  console.log('Received:', systemMessagePayload.data);
  console.assert(systemMessagePayload.content === 'systemMessages.payment.confirmedFirst', 'Test 3 Failed: Incorrect content');
  console.assert(systemMessagePayload.data.username === userH.username, 'Test 3 Failed: Incorrect username');
  console.assert(systemMessagePayload.data.payerUser === userH2.username, 'Test 3 Failed: Incorrect payerUser');
  console.assert(systemMessagePayload.data.receiverUser === userH.username, 'Test 3 Failed: Incorrect receiverUser');
  console.assert(systemMessagePayload.data.amount === '28.5M IRR', 'Test 3 Failed: Incorrect amount');

  // Test: should emit correct system message data when receiver (h2) confirms second payment
  // Simulate transaction state after second payment declared
  let transactionAfterSecondPaymentDeclared = {
    ...mockTransaction,
    status: TransactionStatus.AWAITING_FIRST_PAYER_CONFIRMATION,
    paymentDeclaredAtPayer1: new Date(),
    firstPaymentConfirmedByPayer2At: new Date(),
    paymentDeclaredAtPayer2: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.findUnique.mockResolvedValue(transactionAfterSecondPaymentDeclared as any);

  updatedTransaction = {
    ...transactionAfterSecondPaymentDeclared,
    status: TransactionStatus.COMPLETED,
    secondPaymentConfirmedByPayer1At: new Date(),
  } as Prisma.Transaction;
  prismaMock.transaction.update.mockResolvedValue(updatedTransaction as any);

  await transactionService.confirmReceipt(mockTransaction.id, userH2.id); // h2 confirms h's payment

  systemMessagePayload = getSystemMessagePayload();
  console.log('Test 4: Receiver confirms second payment');
  console.log('Expected:', {
    username: userH2.username,
    payerUser: userH.username,
    receiverUser: userH2.username,
    amount: '500 CAD',
    dueDate: '',
  });
  console.log('Received:', systemMessagePayload.data);
  console.assert(systemMessagePayload.content === 'systemMessages.payment.confirmedSecond', 'Test 4 Failed: Incorrect content');
  console.assert(systemMessagePayload.data.username === userH2.username, 'Test 4 Failed: Incorrect username');
  console.assert(systemMessagePayload.data.payerUser === userH.username, 'Test 4 Failed: Incorrect payerUser');
  console.assert(systemMessagePayload.data.receiverUser === userH2.username, 'Test 4 Failed: Incorrect receiverUser');
  console.assert(systemMessagePayload.data.amount === '500 CAD', 'Test 4 Failed: Incorrect amount');
}

runTests();
