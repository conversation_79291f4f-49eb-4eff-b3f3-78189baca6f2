<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMessage } from 'naive-ui'
import { useTransactionFlowLogic } from '@/composables/useTransactionFlowLogic'
import { useTimerDisplay } from '@/composables/useTimerDisplay'


interface RecipientDetails {
  bankName: string
  accountNumber: string
  accountHolderName: string
  iban?: string
  swiftCode?: string
  routingNumber?: string
  transitNumber?: string
}

interface PaymentInstructions {
  steps: string[]
  notes?: string[]
  estimatedTime?: string
}

interface Props {
  amount: number
  currency: string
  recipientDetails: RecipientDetails
  instructions?: PaymentInstructions
  requiresReference?: boolean
  chatSessionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  requiresReference: true,
  chatSessionId: undefined
})

const emit = defineEmits<{
  paymentDeclared: [{ trackingNumber?: string }]
}>()

const { t } = useI18n()
const message = useMessage()

// Timer Logic (following timer.md guide exactly)
const {
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
} = useTransactionFlowLogic(
  computed(() => props.chatSessionId || null),
  message
);

// Use shared timer display logic
const {
  timerDisplayValue,
  timerColorClass,
  timerLabel
} = useTimerDisplay({
  currentTransaction,
  timeLeft,
  isTimerCritical,
  isTimerExpired,
  isElapsedTimer
});

// State management
const referenceNumber = ref<string>('')
const isSubmitting = ref<boolean>(false)
const showInstructions = ref<boolean>(false)
const showBankDetails = ref<boolean>(true)
const copiedFields = ref<Set<string>>(new Set())

// Computed properties
const formattedAmount = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: props.currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(props.amount)
})

const canSubmit = computed(() => {
  if (props.requiresReference && !referenceNumber.value.trim()) {
    return false
  }
  return !isSubmitting.value
})

const allDetailsText = computed(() => {
  const details = [
    `${t('transactionalChat.payment.bankName')}: ${props.recipientDetails.bankName}`,
    `${t('transactionalChat.payment.accountNumber')}: ${props.recipientDetails.accountNumber}`,
    `${t('transactionalChat.payment.accountHolder')}: ${props.recipientDetails.accountHolderName}`
  ]
  
  if (props.recipientDetails.iban) {
    details.push(`${t('transactionalChat.payment.iban')}: ${props.recipientDetails.iban}`)
  }
  
  if (props.recipientDetails.swiftCode) {
    details.push(`${t('transactionalChat.payment.swiftCode')}: ${props.recipientDetails.swiftCode}`)
  }
  
  if (props.recipientDetails.routingNumber) {
    details.push(`${t('transactionalChat.payment.routingNumber')}: ${props.recipientDetails.routingNumber}`)
  }
  
  if (props.recipientDetails.transitNumber) {
    details.push(`${t('transactionalChat.actionCards.yourTurnToPay.transitNumber')}: ${props.recipientDetails.transitNumber}`)
  }
  
  return details.join('\n')
})

const paymentSteps = computed(() => {
  if (props.instructions?.steps) {
    return props.instructions.steps
  }
  
  // Default payment steps
  return [
    t('transactionalChat.actionCards.yourTurnToPay.step1'),
    t('transactionalChat.actionCards.yourTurnToPay.step2'),
    t('transactionalChat.actionCards.yourTurnToPay.step3'),
    t('transactionalChat.actionCards.yourTurnToPay.step4')
  ]
})

// Methods
const copyToClipboard = async (text: string, fieldName: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success(t('transactionalChat.actionCards.copySuccess'))
    
    // Visual feedback for copied field
    copiedFields.value.add(fieldName)
    setTimeout(() => {
      copiedFields.value.delete(fieldName)
    }, 2000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    message.error(t('transactionalChat.actionCards.copyError'))
  }
}

const copyAllDetails = async () => {
  await copyToClipboard(allDetailsText.value, 'all')
}

const toggleInstructions = () => {
  showInstructions.value = !showInstructions.value
}

const toggleBankDetails = () => {
  showBankDetails.value = !showBankDetails.value
}



const declarePayment = async () => {
  if (!canSubmit.value) return

  isSubmitting.value = true

  try {
    // Map referenceNumber to trackingNumber for backend compatibility
    const declaration: { trackingNumber?: string } = {}

    if (referenceNumber.value.trim()) {
      declaration.trackingNumber = referenceNumber.value.trim()
    }

    emit('paymentDeclared', declaration)
    message.success(t('transactionalChat.actionCards.yourTurnToPay.declared'))
  } catch (error) {
    console.error('Failed to declare payment:', error)
    message.error(t('transactionalChat.actionCards.yourTurnToPay.declareFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const formatFieldValue = (value: string) => {
  // Format account numbers with spaces for better readability
  if (value.length > 8 && /^\d+$/.test(value)) {
    return value.replace(/(\d{4})/g, '$1 ').trim()
  }
  return value
}



const isCopied = (fieldName: string) => {
  return copiedFields.value.has(fieldName)
}
</script>

<template>
  <div class="smart-payment-section" data-testid="smart-payment-section">
    
    <!-- Timer Display (from timer.md guide) -->
    <div v-if="timerDisplayValue"
         class="timer-display"
         :class="timerColorClass"
         data-testid="payment-timer">
      <div class="timer-content">
        <span class="timer-label">{{ timerLabel }}</span>
        <span class="timer-value">{{ timerDisplayValue }}</span>
      </div>
    </div>
    
    <!-- Payment Instructions -->
    

    <!-- Recipient Details -->
    <div class="recipient-details-section">
      <!-- Compact Amount Display -->
      <div class="compact-amount-display">
        <span class="amount-value">{{ formattedAmount }}</span>
        <span class="amount-label">{{ t('transactionalChat.actionCards.yourTurnToPay.sendAmount') }}</span>
      </div>

      <!-- Essential Bank Info (Always Visible) -->
      <div class="essential-bank-info">
        <div class="bank-name-row">
          <span class="bank-name">{{ recipientDetails.bankName }}</span>
          <button
            class="compact-copy-button"
            :class="{ 'copied': isCopied('bank') }"
            @click="copyToClipboard(recipientDetails.bankName, 'bank')"
            data-testid="copy-bank-name"
          >
            {{ isCopied('bank') ? '✓' : '📋' }}
          </button>
        </div>

        <div class="account-row">
          <span class="account-number">{{ formatFieldValue(recipientDetails.accountNumber) }}</span>
          <button
            class="compact-copy-button"
            :class="{ 'copied': isCopied('account') }"
            @click="copyToClipboard(recipientDetails.accountNumber, 'account')"
            data-testid="copy-account-number"
          >
            {{ isCopied('account') ? '✓' : '📋' }}
          </button>
        </div>
      </div>

      <!-- Collapsible Bank Details -->
      <div class="bank-details-toggle">
        <button
          class="toggle-details-button"
          @click="toggleBankDetails"
          data-testid="toggle-bank-details"
        >
          <span class="toggle-icon">{{ showBankDetails ? '▼' : '▶' }}</span>
          {{ showBankDetails ? t('transactionalChat.payment.hideDetails') : t('transactionalChat.payment.showDetails') }}
        </button>
      </div>

      <!-- Expandable Details -->
      <div v-if="showBankDetails" class="expandable-details">
        <div class="detail-row">
          <span class="detail-label">{{ t('transactionalChat.payment.accountHolder') }}:</span>
          <div class="detail-value-with-copy">
            <span class="detail-value">{{ recipientDetails.accountHolderName }}</span>
            <button
              class="mini-copy-button"
              :class="{ 'copied': isCopied('holder') }"
              @click="copyToClipboard(recipientDetails.accountHolderName, 'holder')"
              data-testid="copy-account-holder"
            >
              {{ isCopied('holder') ? '✓' : '📋' }}
            </button>
          </div>
        </div>

        <div v-if="recipientDetails.iban" class="detail-row">
          <span class="detail-label">{{ t('transactionalChat.payment.iban') }}:</span>
          <div class="detail-value-with-copy">
            <span class="detail-value">{{ formatFieldValue(recipientDetails.iban) }}</span>
            <button
              class="mini-copy-button"
              :class="{ 'copied': isCopied('iban') }"
              @click="copyToClipboard(recipientDetails.iban, 'iban')"
              data-testid="copy-iban"
            >
              {{ isCopied('iban') ? '✓' : '📋' }}
            </button>
          </div>
        </div>

        <div v-if="recipientDetails.swiftCode" class="detail-row">
          <span class="detail-label">{{ t('transactionalChat.payment.swiftCode') }}:</span>
          <div class="detail-value-with-copy">
            <span class="detail-value">{{ recipientDetails.swiftCode }}</span>
            <button
              class="mini-copy-button"
              :class="{ 'copied': isCopied('swift') }"
              @click="copyToClipboard(recipientDetails.swiftCode, 'swift')"
              data-testid="copy-swift"
            >
              {{ isCopied('swift') ? '✓' : '📋' }}
            </button>
          </div>
        </div>

        <div v-if="recipientDetails.routingNumber" class="detail-row">
          <span class="detail-label">{{ t('transactionalChat.payment.routingNumber') }}:</span>
          <div class="detail-value-with-copy">
            <span class="detail-value">{{ recipientDetails.routingNumber }}</span>
            <button
              class="mini-copy-button"
              :class="{ 'copied': isCopied('routing') }"
              @click="copyToClipboard(recipientDetails.routingNumber, 'routing')"
              data-testid="copy-routing"
            >
              {{ isCopied('routing') ? '✓' : '📋' }}
            </button>
          </div>
        </div>

        <div v-if="recipientDetails.transitNumber" class="detail-row">
          <span class="detail-label">{{ t('transactionalChat.actionCards.yourTurnToPay.transitNumber') }}:</span>
          <div class="detail-value-with-copy">
            <span class="detail-value">{{ recipientDetails.transitNumber }}</span>
            <button
              class="mini-copy-button"
              :class="{ 'copied': isCopied('transit') }"
              @click="copyToClipboard(recipientDetails.transitNumber, 'transit')"
              data-testid="copy-transit"
            >
              {{ isCopied('transit') ? '✓' : '📋' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Compact Copy All Button -->
      <button
        class="compact-copy-all-button"
        :class="{ 'copied': isCopied('all') }"
        @click="copyAllDetails"
        data-testid="copy-all-details"
      >
        <span class="copy-all-icon">{{ isCopied('all') ? '✓' : '📋' }}</span>
        {{ t('transactionalChat.actionCards.yourTurnToPay.copyAllDetails') }}
      </button>
    </div>

    <!-- Compact Declaration Section -->
    <div class="compact-declaration-section">
      <div class="compact-declaration-field">
        <label class="compact-field-label">
          {{ t('transactionalChat.actionCards.yourTurnToPay.referenceNumber') }}
          <span v-if="requiresReference" class="required-marker">*</span>
        </label>
        <input
          v-model="referenceNumber"
          type="text"
          class="compact-reference-input"
          :placeholder="t('transactionalChat.actionCards.yourTurnToPay.referencePlaceholder')"
          data-testid="reference-number-input"
          maxlength="50"
        />
        <p class="compact-field-hint">
          {{ t('transactionalChat.actionCards.yourTurnToPay.referenceHint') }}
        </p>
      </div>




    </div>

    <!-- Compact Submit Button -->
    <div class="compact-submit-section">
      <button
        class="compact-submit-button"
        :class="{ 'submitting': isSubmitting }"
        @click="declarePayment"
        data-testid="declare-payment-button"
        :disabled="!canSubmit"
      >
        <span v-if="isSubmitting" class="submit-spinner">⟳</span>
        <span class="submit-icon">🚀</span>
        {{ t('transactionalChat.actionCards.yourTurnToPay.button') }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.smart-payment-section {
  display: flex;
  flex-direction: column;
  gap: 16px; /* Reduced gap for mobile */
}

/* Instructions Section */
.instructions-section {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  overflow: hidden;
}

.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.instructions-header:hover {
  background: var(--tc-border-light);
}

.instructions-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

.instructions-toggle {
  background: none;
  border: none;
  color: var(--tc-text-muted);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.instructions-toggle:hover {
  background: var(--tc-bg-subtle);
  color: var(--tc-text-primary);
}

.instructions-toggle.expanded {
  transform: rotate(180deg);
}

.instructions-content {
  padding: 0 16px 16px 16px;
  border-top: 1px solid var(--tc-border-light);
}

.payment-steps {
  margin: 16px 0;
  padding-left: 20px;
}

.payment-step {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--tc-text-secondary);
  line-height: 1.5;
}

.instruction-notes {
  margin: 16px 0;
}

.notes-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--tc-warning);
}

.notes-list {
  margin: 0;
  padding-left: 20px;
}

.note-item {
  margin-bottom: 4px;
  font-size: 13px;
  color: var(--tc-text-muted);
}

.estimated-time {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: var(--tc-primary-light);
  border-radius: 6px;
  font-size: 13px;
  color: var(--tc-primary);
}

.time-icon {
  font-size: 16px;
}

/* Section Titles */
.section-title {
  margin: 0 0 12px 0; /* Reduced margin */
  font-size: 15px; /* Slightly smaller */
  font-weight: 600;
  color: var(--tc-text-primary);
}

/* Compact Amount Display */
.compact-amount-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px; /* Reduced padding */
  background: var(--tc-primary-light);
  border: 1px solid var(--tc-primary);
  border-radius: 6px; /* Smaller radius */
  margin-bottom: 12px; /* Reduced margin */
  text-align: center;
}

.compact-amount-display .amount-value {
  font-size: 20px; /* Slightly larger for emphasis */
  font-weight: 700;
  color: var(--tc-primary);
  font-family: monospace;
  line-height: 1.2;
}

.compact-amount-display .amount-label {
  font-size: 11px; /* Smaller label */
  color: var(--tc-primary);
  font-weight: 500;
  margin-top: 2px;
  opacity: 0.8;
}

/* Essential Bank Info (Always Visible) */
.essential-bank-info {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 6px;
  padding: 10px 12px;
  margin-bottom: 8px;
}

.bank-name-row, .account-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.account-row {
  margin-bottom: 0;
}

.bank-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--tc-text-primary);
}

.account-number {
  font-size: 13px;
  font-family: monospace;
  color: var(--tc-text-secondary);
  font-weight: 500;
}

.compact-copy-button {
  background: var(--tc-border-light);
  border: 1px solid var(--tc-border-medium);
  border-radius: 3px;
  color: var(--tc-text-muted);
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  transition: all 0.2s ease;
  min-width: 28px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compact-copy-button:hover {
  background: var(--tc-primary-light);
  border-color: var(--tc-primary);
  color: var(--tc-primary);
}

.compact-copy-button.copied {
  background: var(--tc-success-light);
  border-color: var(--tc-success);
  color: var(--tc-success);
}

/* Bank Details Toggle */
.bank-details-toggle {
  margin-bottom: 8px;
}

.toggle-details-button {
  background: none;
  border: none;
  color: var(--tc-text-secondary);
  cursor: pointer;
  font-size: 12px;
  padding: 6px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.2s ease;
  width: 100%;
  justify-content: center;
}

.toggle-details-button:hover {
  color: var(--tc-primary);
}

.toggle-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

/* Expandable Details */
.expandable-details {
  background: var(--tc-bg-subtle);
  border: 1px solid var(--tc-border-light);
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding: 0 8px;
  }
  to {
    opacity: 1;
    max-height: 200px;
    padding: 8px;
  }
}

.expandable-details .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
  padding: 0;
  background: none;
  border: none;
}

.expandable-details .detail-row:last-child {
  margin-bottom: 0;
}

.expandable-details .detail-label {
  font-size: 10px;
  color: var(--tc-text-muted);
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.detail-value-with-copy {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  justify-content: flex-end;
}

.expandable-details .detail-value {
  font-size: 11px;
  color: var(--tc-text-primary);
  font-family: monospace;
  font-weight: 500;
  word-break: break-all;
  text-align: right;
}

.mini-copy-button {
  background: var(--tc-border-light);
  border: 1px solid var(--tc-border-medium);
  border-radius: 2px;
  color: var(--tc-text-muted);
  cursor: pointer;
  font-size: 10px;
  padding: 2px 4px;
  transition: all 0.2s ease;
  min-width: 20px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mini-copy-button:hover {
  background: var(--tc-primary-light);
  border-color: var(--tc-primary);
  color: var(--tc-primary);
}

.mini-copy-button.copied {
  background: var(--tc-success-light);
  border-color: var(--tc-success);
  color: var(--tc-success);
}

/* Compact Copy All Button */
.compact-copy-all-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 100%;
  padding: 8px 16px; /* Reduced padding */
  background: var(--tc-primary);
  color: white;
  border: none;
  border-radius: 6px; /* Smaller radius */
  font-size: 12px; /* Smaller font */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.compact-copy-all-button:hover {
  background: var(--tc-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--tc-shadow-sm); /* Smaller shadow */
}

.compact-copy-all-button.copied {
  background: var(--tc-success);
}

.copy-all-icon {
  font-size: 14px; /* Smaller icon */
}

/* Compact Declaration Section */
.compact-declaration-section {
  display: flex;
  flex-direction: column;
  gap: 12px; /* Reduced gap */
}

.compact-declaration-field {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Reduced gap */
}

.compact-field-label {
  font-size: 13px; /* Smaller font */
  font-weight: 500;
  color: var(--tc-text-primary);
}

.required-marker {
  color: var(--tc-danger);
  margin-left: 4px;
}

.compact-reference-input {
  padding: 8px 10px; /* Reduced padding */
  border: 1px solid var(--tc-border-medium);
  border-radius: 4px; /* Smaller radius */
  font-size: 13px; /* Smaller font */
  font-family: monospace;
  transition: border-color 0.2s ease;
}

.compact-reference-input:focus {
  outline: none;
  border-color: var(--tc-primary);
  box-shadow: 0 0 0 1px var(--tc-primary-light);
}

.compact-field-hint {
  margin: 0;
  font-size: 11px; /* Smaller font */
  color: var(--tc-text-muted);
  font-style: italic;
  line-height: 1.3;
}





/* Compact Submit Section */
.compact-submit-section {
  display: flex;
  justify-content: center;
  margin-top: 8px; /* Reduced margin */
}

.compact-submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px; /* Reduced gap */
  padding: 10px 24px; /* Reduced padding */
  background: var(--tc-success);
  color: white;
  border: none;
  border-radius: 6px; /* Smaller radius */
  font-size: 14px; /* Smaller font */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px; /* Reduced height */
  min-width: 160px; /* Reduced width */
}

.compact-submit-button:hover:not(:disabled) {
  background: var(--tc-success-hover);
  transform: translateY(-1px);
  box-shadow: var(--tc-shadow-sm); /* Smaller shadow */
}

.compact-submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.compact-submit-button.submitting {
  cursor: not-allowed;
}

.submit-spinner {
  animation: spin 1s linear infinite;
}

.submit-icon {
  font-size: 16px; /* Smaller icon */
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Mobile-First Responsive Design */
/* Base styles above are mobile-first (320px-767px) */

/* Small mobile optimization (320px-480px) */
@media (max-width: 480px) {
  .smart-payment-section {
    gap: 12px; /* Even smaller gap for very small screens */
  }

  .compact-amount-display {
    padding: 6px 10px;
  }

  .compact-amount-display .amount-value {
    font-size: 18px;
  }

  .essential-bank-info {
    padding: 8px 10px;
  }

  .compact-submit-button {
    min-width: 140px;
    padding: 8px 20px;
    font-size: 13px;
  }
}

/* Tablet enhancement (768px+) */
@media (min-width: 768px) {
  .smart-payment-section {
    gap: 20px; /* Larger gap for tablets */
    max-width: 500px; /* Constrain width on larger screens */
    margin: 0 auto; /* Center the component */
  }

  .compact-amount-display {
    padding: 10px 16px;
  }

  .compact-amount-display .amount-value {
    font-size: 22px;
  }

  .essential-bank-info {
    padding: 12px 16px;
  }

  .expandable-details {
    padding: 10px 12px;
  }

  .compact-submit-button {
    min-width: 180px;
    padding: 12px 28px;
    font-size: 15px;
  }
}

/* Desktop enhancement (1024px+) */
@media (min-width: 1024px) {
  .smart-payment-section {
    gap: 24px;
    max-width: 600px;
  }

  .timer-display {
    padding: 10px 16px;
  }

  .timer-value {
    font-size: 1.2em;
  }

  .timer-label {
    font-size: 0.8em;
  }
}

/* Compact Timer Display Styles (Mobile-First) */
.timer-display {
  text-align: center;
  padding: 8px 12px; /* Reduced padding */
  border-radius: 6px; /* Smaller radius */
  margin: 8px 0; /* Reduced margin */
  border: 2px solid #10b981; /* Default green for plenty of time */
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  transition: all 0.3s ease;
  font-weight: 600;
}

/* Plenty of time - Green (>1.5 hours) */
.timer-display.timer-plenty {
  background-color: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: #10b981;
}

/* Moderate time - Light Green (1-1.5 hours) */
.timer-display.timer-moderate {
  background-color: rgba(34, 197, 94, 0.1);
  color: #166534;
  border-color: #22c55e;
}

/* Warning - Orange (30min-1hour) */
.timer-display.timer-warning {
  background-color: rgba(251, 146, 60, 0.1);
  color: #c2410c;
  border-color: #fb923c;
}

/* Critical - Red with pulse (<30 minutes) */
.timer-display.timer-critical {
  background-color: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: #ef4444;
  animation: pulse 2s infinite;
}

/* Expired - Dark Red (time ran out) */
.timer-display.timer-expired {
  background-color: rgba(245, 101, 101, 0.2);
  color: #991b1b;
  border-color: #f56565;
  border-style: dashed;
}

/* Elapsed - Gray (counting up after expiry) */
.timer-display.timer-elapsed {
  background-color: rgba(156, 163, 175, 0.1);
  color: #4b5563;
  border-color: #9ca3af;
  border-style: dotted;
}

/* Default fallback */
.timer-display.timer-default {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border-color: #3b82f6;
}

.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px; /* Reduced gap */
}

.timer-label {
  font-size: 0.75em; /* Smaller font */
  font-weight: 500;
}

.timer-value {
  font-size: 1.1em; /* Smaller font */
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

/* RTL Support */
[dir="rtl"] .detail-info {
  flex-direction: row-reverse;
}

[dir="rtl"] .detail-content {
  text-align: right;
}

[dir="rtl"] .payment-steps {
  padding-right: 20px;
  padding-left: 0;
}

[dir="rtl"] .notes-list {
  padding-right: 20px;
  padding-left: 0;
}

[dir="rtl"] .instructions-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .amount-display {
  flex-direction: row-reverse;
}
</style>
