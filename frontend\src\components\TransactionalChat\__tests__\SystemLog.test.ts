import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import SystemLog from '../SystemLog.vue'
import type { FeedItem } from '@/stores/transactionalChat/transactionalChatStore'

// Mock translation messages
const messages = {
  en: {
    systemMessages: {
      payment: {
        declared: 'Payment declaration submitted by {username} for {amount}. Receipt confirmation pending from {otherUser}.',
        confirmedFirst: 'First payment receipt confirmed by {username}. Second payment phase initiated for {payerUser} to {receiverUser}. Payment deadline: {dueDate}.',
        confirmedSecond: 'Second payment receipt confirmed by {username}. Transaction completed.'
      },
      transaction: {
        complete: 'Transaction completed successfully. 🎉'
      },
      proposal: {
        bothAgreed: 'Mutual agreement reached. {firstPayer} designated as first payer. Payment deadline: {dueDate}.'
      }
    }
  },
  fa: {
    systemMessages: {
      payment: {
        declared: 'اعلام پرداخت توسط {username} برای مبلغ {amount} ثبت شد. تأیید دریافت از {otherUser} در انتظار است.',
        confirmedFirst: 'تأیید دریافت پرداخت اول توسط {username} انجام شد. مرحله پرداخت دوم برای {payerUser} به {receiverUser} آغاز شد. مهلت پرداخت: {dueDate}.',
        confirmedSecond: 'تأیید دریافت پرداخت دوم توسط {username} انجام شد. معامله تکمیل شد.'
      },
      transaction: {
        complete: 'معامله با موفقیت تکمیل شد. 🎉'
      },
      proposal: {
        bothAgreed: 'توافق متقابل حاصل شد. {firstPayer} به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: {dueDate}.'
      }
    }
  }
}

describe('SystemLog.vue', () => {
  let i18n: any

  beforeEach(() => {
    i18n = createI18n({
      legacy: false,
      locale: 'en',
      fallbackLocale: 'en',
      messages
    })
  })

  const createWrapper = (item: FeedItem, locale = 'en') => {
    i18n.global.locale.value = locale
    return mount(SystemLog, {
      props: { item },
      global: {
        plugins: [i18n]
      }
    })
  }

  it('renders system message with translation key correctly', () => {
    const item: FeedItem = {
      id: 'test-1',
      type: 'systemLog',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'systemMessages.payment.declared',
      data: {
        username: 'Alice',
        otherUser: 'Bob',
        amount: '500 CAD'
      }
    }

    const wrapper = createWrapper(item)
    
    expect(wrapper.find('[data-testid="system-message-text"]').text())
      .toBe('Payment declaration submitted by Alice for 500 CAD. Receipt confirmation pending from Bob.')
  })

  it('renders system message in Persian correctly', () => {
    const item: FeedItem = {
      id: 'test-2',
      type: 'systemLog',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'systemMessages.payment.declared',
      data: {
        username: 'علی',
        otherUser: 'فاطمه',
        amount: '500 CAD'
      }
    }

    const wrapper = createWrapper(item, 'fa')
    
    expect(wrapper.find('[data-testid="system-message-text"]').text())
      .toBe('اعلام پرداخت توسط علی برای مبلغ 500 CAD ثبت شد. تأیید دریافت از فاطمه در انتظار است.')
  })

  it('formats currency amounts correctly', () => {
    const item: FeedItem = {
      id: 'test-3',
      type: 'systemLog',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'systemMessages.payment.declared',
      data: {
        username: 'Alice',
        otherUser: 'Bob',
        amount: 28500000,
        currency: 'IRR'
      }
    }

    const wrapper = createWrapper(item)
    
    // Should format large IRR amounts in millions
    expect(wrapper.find('[data-testid="system-message-text"]').text())
      .toContain('28.5M IRR')
  })

  it('detects celebration messages correctly', () => {
    const item: FeedItem = {
      id: 'test-4',
      type: 'systemLog',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'systemMessages.transaction.complete'
    }

    const wrapper = createWrapper(item)
    
    expect(wrapper.find('.system-log').classes()).toContain('celebration')
    expect(wrapper.find('[data-testid="system-icon"]').text()).toBe('🎉')
  })

  it('handles legacy non-translation-key messages gracefully', () => {
    const item: FeedItem = {
      id: 'test-5',
      type: 'systemLog',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'Some legacy hardcoded message'
    }

    const wrapper = createWrapper(item)
    
    // Should display the message as-is and log warning
    expect(wrapper.find('[data-testid="system-message-text"]').text())
      .toBe('Some legacy hardcoded message')
  })

  it('displays timestamp correctly', () => {
    const item: FeedItem = {
      id: 'test-6',
      type: 'systemLog',
      timestamp: '2023-01-01T14:30:00Z',
      message: 'systemMessages.transaction.complete'
    }

    const wrapper = createWrapper(item)
    
    // Should format time as HH:MM
    expect(wrapper.find('[data-testid="system-message-timestamp"]').text())
      .toMatch(/\d{1,2}:\d{2}/)
  })
})
