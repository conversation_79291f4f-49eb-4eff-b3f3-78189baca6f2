
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { FeedItem } from '@/stores/transactionalChat/transactionalChatStore';

export function useSystemMessageProcessor(item: FeedItem) {
  const { t } = useI18n();

  const processedMessage = computed(() => {
    if (!item.message) {
      return '';
    }

    const messageKey = item.message.trim();
    const params = item.data || {};

    // Simply translate the message key with the data payload it came with.
    // The backend is now the single source of truth for the content.
    return t(messageKey, params);
  });

  return {
    processedMessage,
  };
}

