{"step": {"paymentInfoCompleted": "اطلاعات پرداخت توسط هر دو طرف ارائه شده است.", "negotiationStarted": "جمع‌آوری اطلاعات پرداخت تکمیل شد. مرحله تعیین پرداخت‌کننده اول آغاز شد.", "agreementReached": "تعیین پرداخت‌کننده اول تکمیل شد: {firstPayer} به عنوان پرداخت‌کننده اولیه تعیین شد.", "firstPaymentPhase": "مرحله پرداخت اول آغاز شد. {firstPayer} باید {amount} ارسال کند.", "firstPaymentDeclared": "اعلام پرداخت توسط {username} برای مبلغ {amount} ثبت شد.", "firstPaymentConfirmed": "تأیید دریافت پرداخت اول انجام شد. مرحله پرداخت دوم آغاز شد.", "secondPaymentPhase": "مرحله پرداخت دوم آغاز شد. {secondPayer} باید {amount} ارسال کند.", "secondPaymentDeclared": "اعلام پرداخت توسط {username} برای مبلغ {amount} ثبت شد.", "secondPaymentConfirmed": "تأیید دریافت پرداخت دوم انجام شد. معامله تکمیل شد.", "transactionFinalized": "🎉 معامله با موفقیت تکمیل شد."}, "proposal": {"from": "پیشنهاد تعیین پرداخت‌کننده اول توسط {username} ارسال شد: \"{message}\"", "agreed": "پیشنهاد توسط {username} پذیرفته شد.", "counterOffer": "پیشنهاد متقابل از {username}: تغییر پرداخت‌کننده اول.", "bothAgreed": "توافق متقابل حاصل شد. {firstPayer} به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: {dueDate}.", "bothAgreedSystemDesignated": "شرایط معامله توسط هر دو طرف پذیرفته شد. {firstPayer} توسط سیستم به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: {dueDate}."}, "payment": {"declared": "اعلام پرداخت توسط {username} برای مبلغ {amount} ثبت شد. تأیید دریافت از {otherUser} در انتظار است.", "confirmedFirst": "تأیید دریافت پرداخت اول توسط {username} انجام شد. مرحله پرداخت دوم برای {payerUser} به {receiverUser} آغاز شد. مهلت پرداخت: {dueDate}.", "confirmedSecond": "تأیید دریافت پرداخت نهایی توسط {username} انجام شد. معامله تکمیل شد."}, "transaction": {"complete": "معامله با موفقیت تکمیل شد. 🎉", "cancelled": "معامله توسط {username} لغو شد.", "disputed": "معامله توسط {username} مورد اعتراض قرار گرفت."}, "transactionalChat": {"systemLogs": {"paymentDetailsProvided": "جزئیات پرداخت توسط {username} ارسال شد.", "readyToNegotiate": "جمع‌آوری اطلاعات پرداخت تکمیل شد. مرحله تعیین پرداخت‌کننده اول آغاز شد.", "secondPaymentDeclared": "اعلام پرداخت توسط {username} برای مبلغ {amount} ثبت شد.", "ratingSubmitted": "امتیازدهی تجربه توسط {username} ثبت شد."}}}