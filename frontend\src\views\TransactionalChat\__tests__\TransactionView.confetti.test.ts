import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import TransactionView from '../TransactionView.vue'
import { TransactionStatusEnum } from '@/types/transaction'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'

// Mock the route
vi.mock('vue-router', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useRoute: () => ({
      params: { transactionId: 'test-transaction-id' }
    })
  }
})

// Mock the components to avoid complex dependencies
vi.mock('@/components/TransactionalChat/TheHeaderBar.vue', () => ({
  default: { template: '<div data-testid="header-bar-mock"></div>' }
}))

vi.mock('@/components/TransactionalChat/TheSmartStatusBar.vue', () => ({
  default: { template: '<div data-testid="status-bar-mock"></div>' }
}))

vi.mock('@/components/TransactionalChat/TheUnifiedFeed.vue', () => ({
  default: { template: '<div data-testid="unified-feed-mock"></div>' }
}))

vi.mock('@/components/TransactionalChat/TheDynamicActionBar.vue', () => ({
  default: { template: '<div data-testid="action-bar-mock"></div>' }
}))

vi.mock('@/components/TransactionalChatPaymentMethodSelector.vue', () => ({
  default: { template: '<div data-testid="payment-method-selector-mock"></div>' }
}))

describe('TransactionView - Confetti Animation', () => {
  let pinia: any
  let transactionalChatStore: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    // Mock timers for confetti timeout
    vi.useFakeTimers()
    
    // Get the store instance
    transactionalChatStore = useTransactionalChatStore()
    
    // Mock store methods
    transactionalChatStore.reset = vi.fn()
    transactionalChatStore.fetchTransaction = vi.fn()
    transactionalChatStore.stopTimer = vi.fn()
    
    // Mock window object for confetti responsiveness
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    })
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 800,
    })
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.clearAllMocks()
  })

  const createWrapper = () => {
    return mount(TransactionView, {
      global: {
        plugins: [pinia],
        stubs: {
          'ConfettiExplosion': {
            template: '<div data-testid="confetti-explosion"></div>',
            props: ['particleCount', 'force', 'duration', 'colors', 'particleSize', 'stageWidth', 'stageHeight', 'shouldDestroyAfterDone']
          }
        }
      }
    })
  }

  it('should render confetti container', () => {
    const wrapper = createWrapper()
    
    const confettiContainer = wrapper.find('.confetti-container')
    expect(confettiContainer.exists()).toBe(true)
    expect(confettiContainer.classes()).toContain('confetti-container')
  })

  it('should not show confetti initially', () => {
    const wrapper = createWrapper()
    
    const confetti = wrapper.find('[data-testid="confetti-explosion"]')
    expect(confetti.exists()).toBe(false)
  })

  it('should trigger confetti when handleTransactionCompleted is called', async () => {
    const wrapper = createWrapper()

    // Verify confetti is not shown initially
    expect(wrapper.find('[data-testid="confetti-explosion"]').exists()).toBe(false)

    // Get the component instance and call the method directly
    const vm = wrapper.vm as any
    await vm.handleTransactionCompleted()

    await nextTick()

    // Verify confetti is now shown
    const confetti = wrapper.find('[data-testid="confetti-explosion"]')
    expect(confetti.exists()).toBe(true)
  })

  it('should configure confetti with correct props', async () => {
    const wrapper = createWrapper()

    // Trigger confetti directly
    const vm = wrapper.vm as any
    await vm.handleTransactionCompleted()

    await nextTick()

    const confetti = wrapper.find('[data-testid="confetti-explosion"]')
    expect(confetti.exists()).toBe(true)

    // Check confetti props
    const confettiComponent = confetti.vm as any
    expect(confettiComponent.$props.particleCount).toBe(150)
    expect(confettiComponent.$props.force).toBe(0.3)
    expect(confettiComponent.$props.duration).toBe(3000)
    expect(confettiComponent.$props.colors).toEqual(['#18a058', '#2080f0', '#f0a020', '#d03050', '#9c26b0', '#ff6b35'])
    expect(confettiComponent.$props.particleSize).toBe(8)
    expect(confettiComponent.$props.stageWidth).toBe(1200)
    expect(confettiComponent.$props.stageHeight).toBe(800)
    expect(confettiComponent.$props.shouldDestroyAfterDone).toBe(true)
  })

  it('should automatically hide confetti after timeout', async () => {
    const wrapper = createWrapper()

    // Trigger confetti directly
    const vm = wrapper.vm as any
    await vm.handleTransactionCompleted()

    await nextTick()

    // Verify confetti is shown
    expect(wrapper.find('[data-testid="confetti-explosion"]').exists()).toBe(true)

    // Fast-forward time to trigger timeout
    vi.advanceTimersByTime(3500)
    await nextTick()

    // Verify confetti is hidden
    expect(wrapper.find('[data-testid="confetti-explosion"]').exists()).toBe(false)
  })

  it('should prevent duplicate confetti for the same transaction', async () => {
    const wrapper = createWrapper()
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    // Set transaction details
    transactionalChatStore.transactionDetails = {
      id: 'test-transaction-id',
      status: TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT
    }
    
    await nextTick()
    
    // Change to COMPLETED first time
    transactionalChatStore.transactionDetails = {
      id: 'test-transaction-id',
      status: TransactionStatusEnum.COMPLETED
    }
    
    await nextTick()
    
    // Verify confetti triggered
    expect(consoleSpy).toHaveBeenCalledWith('🎉 Transaction completed! Triggering confetti for transaction:', 'test-transaction-id')
    
    // Reset console spy
    consoleSpy.mockClear()
    
    // Try to trigger again with same transaction
    transactionalChatStore.transactionDetails = {
      id: 'test-transaction-id',
      status: TransactionStatusEnum.COMPLETED
    }
    
    await nextTick()
    
    // Verify confetti was NOT triggered again
    expect(consoleSpy).not.toHaveBeenCalledWith('🎉 Transaction completed! Triggering confetti for transaction:', 'test-transaction-id')
    
    consoleSpy.mockRestore()
  })

  it('should update window dimensions on resize', async () => {
    const wrapper = createWrapper()

    // Trigger confetti to render the component
    const vm = wrapper.vm as any
    await vm.handleTransactionCompleted()

    await nextTick()

    // Simulate window resize
    Object.defineProperty(window, 'innerWidth', { value: 800 })
    Object.defineProperty(window, 'innerHeight', { value: 600 })

    // Trigger resize event
    window.dispatchEvent(new Event('resize'))
    await nextTick()

    const confetti = wrapper.find('[data-testid="confetti-explosion"]')
    if (confetti.exists()) {
      const confettiComponent = confetti.vm as any
      expect(confettiComponent.$props.stageWidth).toBe(800)
      expect(confettiComponent.$props.stageHeight).toBe(600)
    }
  })

  it('should clean up completed transactions set on unmount', () => {
    const wrapper = createWrapper()
    
    // Trigger confetti to add transaction to set
    transactionalChatStore.transactionDetails = {
      id: 'test-transaction-id',
      status: TransactionStatusEnum.COMPLETED
    }
    
    // Unmount component
    wrapper.unmount()
    
    // This test verifies the cleanup happens without errors
    expect(true).toBe(true)
  })
})
