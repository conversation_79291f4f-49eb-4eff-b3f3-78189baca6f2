<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SystemLog.vue Visual Demonstration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-container {
            padding: 24px;
            background: #f8fafc;
            min-height: 600px;
        }
        
        .language-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
            gap: 8px;
        }
        
        .lang-btn {
            padding: 8px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .lang-btn.active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }
        
        .system-log {
            display: flex;
            justify-content: center;
            margin: 16px 0;
            animation: fadeIn 0.5s ease-out;
        }
        
        .system-content {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            max-width: 80%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .celebration .system-content {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.05));
            border-color: #10b981;
            animation: celebrationPulse 0.6s ease-out;
        }
        
        .system-icon {
            flex-shrink: 0;
            font-size: 16px;
            line-height: 1;
            margin-top: 1px;
        }
        
        .celebration-icon {
            animation: bounce 0.8s ease-out;
        }
        
        .info-icon {
            opacity: 0.7;
        }
        
        .system-message {
            flex: 1;
            min-width: 0;
        }
        
        .message-text {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            color: #475569;
            font-weight: 500;
            text-align: center;
        }
        
        .celebration .message-text {
            color: #059669;
            font-weight: 600;
        }
        
        .message-timestamp {
            font-size: 11px;
            color: #94a3b8;
            display: block;
            text-align: center;
            margin-top: 4px;
        }
        
        .step-indicator {
            background: #4f46e5;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin: 24px auto 16px;
            max-width: 300px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0.3; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes celebrationPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-8px); }
            60% { transform: translateY(-4px); }
        }
        
        .rtl {
            direction: rtl;
        }
        
        .rtl .system-content {
            direction: rtl;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>SystemLog.vue Improved Messages</h1>
            <p>Neutral, Professional, Third-Person System Messages</p>
        </div>
        
        <div class="chat-container">
            <div class="language-toggle">
                <button class="lang-btn active" onclick="showLanguage('en')">English</button>
                <button class="lang-btn" onclick="showLanguage('fa')">فارسی</button>
            </div>
            
            <!-- English Messages -->
            <div id="en-messages">
                <div class="step-indicator">Step 1: Transaction Initiation</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Payment information has been provided by both parties.</p>
                            <span class="message-timestamp">21:15</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 2: Negotiation Phase</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Payment information collection complete. First payer designation phase initiated.</p>
                            <span class="message-timestamp">21:16</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 3: Agreement Reached</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Transaction terms accepted by both parties. Alice designated as first payer by system. Payment deadline: 2025-01-10, 22:16.</p>
                            <span class="message-timestamp">21:17</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 4: First Payment Declaration</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Payment declaration submitted by Alice. Receipt confirmation pending from محمد.</p>
                            <span class="message-timestamp">21:45</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 5: First Payment Confirmed</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">First payment receipt confirmed by محمد. Second payment phase initiated for محمد to Alice. Payment deadline: 2025-01-11, 22:45.</p>
                            <span class="message-timestamp">22:10</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 6: Second Payment Declaration</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Payment declaration submitted by محمد. Receipt confirmation pending from Alice.</p>
                            <span class="message-timestamp">22:30</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 7: Second Payment Confirmed</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">Second payment receipt confirmed by Alice. Transaction completed.</p>
                            <span class="message-timestamp">22:45</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">Step 8: Transaction Complete</div>
                <div class="system-log celebration">
                    <div class="system-content">
                        <div class="system-icon"><span class="celebration-icon">🎉</span></div>
                        <div class="system-message">
                            <p class="message-text">Transaction completed successfully. 🎉</p>
                            <span class="message-timestamp">22:45</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Persian Messages -->
            <div id="fa-messages" class="hidden rtl">
                <div class="step-indicator">مرحله ۱: آغاز معامله</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">اطلاعات پرداخت توسط هر دو طرف ارائه شده است.</p>
                            <span class="message-timestamp">21:15</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۲: مرحله مذاکره</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">جمع‌آوری اطلاعات پرداخت تکمیل شد. مرحله تعیین پرداخت‌کننده اول آغاز شد.</p>
                            <span class="message-timestamp">21:16</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۳: حصول توافق</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">شرایط معامله توسط هر دو طرف پذیرفته شد. Alice توسط سیستم به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: 2025-01-10, 22:16.</p>
                            <span class="message-timestamp">21:17</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۴: اعلام پرداخت اول</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">اعلام پرداخت توسط Alice ثبت شد. تأیید دریافت از محمد در انتظار است.</p>
                            <span class="message-timestamp">21:45</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۵: تأیید پرداخت اول</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">تأیید دریافت پرداخت اول توسط محمد انجام شد. مرحله پرداخت دوم برای محمد به Alice آغاز شد. مهلت پرداخت: 2025-01-11, 22:45.</p>
                            <span class="message-timestamp">22:10</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۶: اعلام پرداخت دوم</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">اعلام پرداخت توسط محمد ثبت شد. تأیید دریافت از Alice در انتظار است.</p>
                            <span class="message-timestamp">22:30</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۷: تأیید پرداخت دوم</div>
                <div class="system-log">
                    <div class="system-content">
                        <div class="system-icon"><span class="info-icon">ℹ️</span></div>
                        <div class="system-message">
                            <p class="message-text">تأیید دریافت پرداخت دوم توسط Alice انجام شد. معامله تکمیل شد.</p>
                            <span class="message-timestamp">22:45</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-indicator">مرحله ۸: تکمیل معامله</div>
                <div class="system-log celebration">
                    <div class="system-content">
                        <div class="system-icon"><span class="celebration-icon">🎉</span></div>
                        <div class="system-message">
                            <p class="message-text">معامله با موفقیت تکمیل شد. 🎉</p>
                            <span class="message-timestamp">22:45</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showLanguage(lang) {
            // Update button states
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide message containers
            document.getElementById('en-messages').classList.toggle('hidden', lang !== 'en');
            document.getElementById('fa-messages').classList.toggle('hidden', lang !== 'fa');
        }
    </script>
</body>
</html>
