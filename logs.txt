backend log:
[TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer 200 59ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions 204 0ms
<-- POST /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions
[TransactionalChatRoutes] POST /:transactionId/actions - declareSecondPayerPayment for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Performing action declareSecondPayerPayment for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpopk0000vlpgl7qffd1c
[TransactionService] User cmckmpopk0000vlpgl7qffd1c declaring payment for transaction cmcxkt7ub0006vlp0kk66k1l6
[TransactionService] DEBUG: Emitting payload with usernames - A: "h2", B: "h"
[TransactionService] Emitted TRANSACTION_STATUS_UPDATED for transaction cmcxkt7ub0006vlp0kk66k1l6 to user rooms cmckmpopk0000vlpgl7qffd1c and cmckmpoq60001vlpgo82crgdt
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg',
  messageContent: 'systemMessages.payment.declared',
  transactionId: 'cmcxkt7ub0006vlp0kk66k1l6',
  messageData: '{\n  "username": "h",\n  "otherUser": "h2",\n  "amount": "57.0M IRR"\n}'      
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxkuf8f000qvlp0k8aa8had",\n' +
    '  "chatSessionId": "cmcxkt7t50005vlp0ebdrwwfg",\n' +
    '  "content": "systemMessages.payment.declared",\n' +
    '  "createdAt": "2025-07-10T16:03:13.695Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",\n' +
    '  "data": {\n' +
    '    "username": "h",\n' +
    '    "otherUser": "h2",\n' +
    '    "amount": "57.0M IRR"\n' +
    '  }\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxkt7ub0006vlp0kk66k1l6
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxkt7t50005vlp0ebdrwwfg, transaction cmcxkt7ub0006vlp0kk66k1l6: systemMessages.payment.declared
[NotificationService] Emitted NEW_NOTIFICATION to userId: cmckmpoq60001vlpgo82crgdt
[NotificationService] Notification created: f4552bda-6cd3-4275-9062-0bb008842710 for user cmckmpoq60001vlpgo82crgdt
[NotificationService] Emitted NEW_NOTIFICATION to userId: cmckmpopk0000vlpgl7qffd1c
[NotificationService] Notification created: 677f946b-2285-40f0-8b2e-ac866a1f4ca8 for user cmckmpopk0000vlpgl7qffd1c
--> POST /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions 200 82ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 204 1ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 204 1ms
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
[TransactionalChatRoutes] GET /:transactionId - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpopk0000vlpgl7qffd1c
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
[TransactionalChatRoutes] GET /:transactionId - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpoq60001vlpgo82crgdt
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 200 48ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 200 60ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer 204 1ms
<-- OPTIONS /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg
--> OPTIONS /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg 204 2ms
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer
[TransactionalChatRoutes] GET /:transactionId/timer - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpoq60001vlpgo82crgdt
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer 204 1ms
<-- GET /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg
[TransactionService] Initialized with IO, NotificationService, ChatService, and optional MatchingService
<-- OPTIONS /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg
--> OPTIONS /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg 204 1ms
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer
[TransactionalChatRoutes] GET /:transactionId/timer - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpopk0000vlpgl7qffd1c
<-- GET /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg
[TransactionService] Initialized with IO, NotificationService, ChatService, and optional MatchingService
[TransactionService] Fetched transaction by chatSessionId: cmcxkt7t50005vlp0ebdrwwfg (TX ID: cmcxkt7ub0006vlp0kk66k1l6) for user cmckmpoq60001vlpgo82crgdt
--> GET /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg 200 24ms
[TransactionService] Fetched transaction by chatSessionId: cmcxkt7t50005vlp0ebdrwwfg (TX ID: cmcxkt7ub0006vlp0kk66k1l6) for user cmckmpopk0000vlpgl7qffd1c
--> GET /api/transactions/chat/cmcxkt7t50005vlp0ebdrwwfg 200 17ms
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer 200 82ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/timer 200 209ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions 204 0ms
<-- POST /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions
[TransactionalChatRoutes] POST /:transactionId/actions - confirmFirstPayerPayment for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Performing action confirmFirstPayerPayment for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpoq60001vlpgo82crgdt
[TransactionService] User cmckmpoq60001vlpgo82crgdt confirming receipt for transaction cmcxkt7ub0006vlp0kk66k1l6
[TransactionService] DEBUG: Emitting payload with usernames - A: "h2", B: "h"
[TransactionService] Emitted TRANSACTION_STATUS_UPDATED for transaction cmcxkt7ub0006vlp0kk66k1l6 to user rooms cmckmpopk0000vlpgl7qffd1c and cmckmpoq60001vlpgo82crgdt
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg',
  messageContent: 'systemMessages.payment.confirmedSecond',
  transactionId: 'cmcxkt7ub0006vlp0kk66k1l6',
  messageData: '{\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2",\n' +
    '  "amount": "57.0M IRR",\n' +
    '  "dueDate": ""\n' +
    '}'
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxkukfz000svlp0bm1ibel2',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:03:20.447Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxkukfz000svlp0bm1ibel2",\n' +
    '  "chatSessionId": "cmcxkt7t50005vlp0ebdrwwfg",\n' +
    '  "content": "systemMessages.payment.confirmedSecond",\n' +
    '  "createdAt": "2025-07-10T16:03:20.447Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",\n' +
    '  "data": {\n' +
    '    "username": "h2",\n' +
    '    "payerUser": "h",\n' +
    '    "receiverUser": "h2",\n' +
    '    "amount": "57.0M IRR",\n' +
    '    "dueDate": ""\n' +
    '  }\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxkt7ub0006vlp0kk66k1l6
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxkt7t50005vlp0ebdrwwfg, transaction cmcxkt7ub0006vlp0kk66k1l6: systemMessages.payment.confirmedSecond
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg',
  messageContent: 'systemMessages.transaction.complete',
  transactionId: 'cmcxkt7ub0006vlp0kk66k1l6',
  messageData: '{}'
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxkukga000uvlp0tm2d7b8u',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:03:20.458Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxkukga000uvlp0tm2d7b8u",\n' +
    '  "chatSessionId": "cmcxkt7t50005vlp0ebdrwwfg",\n' +
    '  "content": "systemMessages.transaction.complete",\n' +
    '  "createdAt": "2025-07-10T16:03:20.458Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",\n' +
    '  "data": {}\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxkt7ub0006vlp0kk66k1l6
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxkt7t50005vlp0ebdrwwfg, transaction cmcxkt7ub0006vlp0kk66k1l6: systemMessages.transaction.complete
🔍 [TransactionalChatService] addSystemMessage called: {
  chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg',
  messageKey: 'transactionalChat.systemLogs.firstPaymentConfirmed',
  transactionId: 'cmcxkt7ub0006vlp0kk66k1l6',
  params: undefined
}
🔍 [TransactionalChatService] Created database message: {
  messageId: 'cmcxkukgj000wvlp07xdc8llm',
  content: 'transactionalChat.systemLogs.firstPaymentConfirmed',
  createdAt: '2025-07-10T16:03:20.467Z'
}
🔍 [TransactionalChatService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxkukgj000wvlp07xdc8llm",\n' +
    '  "chatSessionId": "cmcxkt7t50005vlp0ebdrwwfg",\n' +
    '  "content": "transactionalChat.systemLogs.firstPaymentConfirmed",\n' +
    '  "createdAt": "2025-07-10T16:03:20.467Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6"\n' +
    '}'
}
--> POST /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6/actions 200 77ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 204 1ms
<-- OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
--> OPTIONS /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 204 0ms
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
[TransactionalChatRoutes] GET /:transactionId - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpopk0000vlpgl7qffd1c
<-- GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6
[TransactionalChatRoutes] GET /:transactionId - cmcxkt7ub0006vlp0kk66k1l6 for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxkt7ub0006vlp0kk66k1l6, user cmckmpoq60001vlpgo82crgdt
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:32.034Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktj35000avlp0nhabq5zh',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:02:36.341Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktmet000cvlp05avy556x, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmet000cvlp05avy556x',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:02:36.402Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktmgi000evlp0sqcfmfh9',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:40.853Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktpw5000gvlp01cc2wdvh, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukfz000svlp0bm1ibel2',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:03:20.447Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukfz000svlp0bm1ibel2',
  messageData: '{\n' +
    '  "amount": "57.0M IRR",\n' +
    '  "dueDate": "",\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukga000uvlp0tm2d7b8u',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:03:20.458Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxkukga000uvlp0tm2d7b8u, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.transaction.complete
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktpw5000gvlp01cc2wdvh',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:02:44.362Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxktsll000ivlp0jw50b4lb, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukga000uvlp0tm2d7b8u',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukgj000wvlp07xdc8llm',
  content: 'transactionalChat.systemLogs.firstPaymentConfirmed',
  createdAt: '2025-07-10T16:03:20.467Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxkukgj000wvlp07xdc8llm, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.firstPaymentConfirmed
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsll000ivlp0jw50b4lb',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:02:44.462Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxktsoe000kvlp0fb6g1hjd',
  messageData: '{\n  "dueDate": "10/07/2025, 19:02:44",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:02:53.995Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku017000mvlp0iqgl32l8',
  messageData: '{\n  "amount": "1,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:03:03.576Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxku7fc000ovlp0px3d2urt',
  messageData: '{\n' +
    '  "amount": "1,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:03:03",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:03:13.695Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkuf8f000qvlp0k8aa8had',
  messageData: '{\n  "amount": "57.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukfz000svlp0bm1ibel2',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:03:20.447Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukfz000svlp0bm1ibel2',
  messageData: '{\n' +
    '  "amount": "57.0M IRR",\n' +
    '  "dueDate": "",\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukga000uvlp0tm2d7b8u',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:03:20.458Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxkukga000uvlp0tm2d7b8u, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.transaction.complete
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukgj000wvlp07xdc8llm',
  messageData: '{\n  "username": "h",\n  "amount": "1,000 CAD"\n}',
  wasReconstructed: true
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 200 49ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukga000uvlp0tm2d7b8u',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxkukgj000wvlp07xdc8llm',
  content: 'transactionalChat.systemLogs.firstPaymentConfirmed',
  createdAt: '2025-07-10T16:03:20.467Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxkukgj000wvlp07xdc8llm, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.firstPaymentConfirmed
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxkukgj000wvlp07xdc8llm',
  messageData: '{\n  "username": "h",\n  "amount": "1,000 CAD"\n}',
  wasReconstructed: true
}
--> GET /api/transactional-chat/cmcxkt7ub0006vlp0kk66k1l6 200 53ms



User A frontend log:
ActionCard.vue:254 🎯 ActionCard - handlePaymentInfoConfirmed called: {method: Proxy(Object), validationStatus: 'complete'}
transactionalChatApi.ts:201 🔄 API: Performing action: paymentInfo for transaction: cmcxkt7ub0006vlp0kk66k1l6
SmartPaymentInfoSection.vue:105 🔄 SmartPaymentInfoSection - currentMethod watcher: {newMethod: Proxy(Object), oldMethod: Proxy(Object), selectedMethodId: 'cmckmw5p10001vl6sbvvcqz1v'}
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxjjnlx0001vl6kflbxdngm interests to PENDING_RESPONSE
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to PENDING_AGREEMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktj35000avlp0nhabq5zh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:32.034Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktj35000avlp0nhabq5zh",\n  "c…0kk66k1l6",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktj35000avlp0nhabq5zh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:32.034Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktj35000avlp0nhabq5zh",\n  "type": "…sProvided",\n  "data": {\n    "username": "h"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 1
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 1
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}}
SmartPaymentInfoSection.vue:363 🔄 SmartPaymentInfoSection unmounted
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:779 ⚠️ No socket update received within 800ms, performing manual refetch
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 1
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 1
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to AWAITING_FIRST_PAYER_DESIGNATION
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktmet000cvlp05avy556x', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:02:36.341Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktmet000cvlp05avy556x",\n  "c…nId": "cmcxkt7ub0006vlp0kk66k1l6",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktmet000cvlp05avy556x', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:02:36.341Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktmet000cvlp05avy556x",\n  "type": "…Chat.systemLogs.readyToNegotiate",\n  "data": {}\n}'}
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 1
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 3
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_DESIGNATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: negotiation
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-negotiation', type: 'actionCard', timestamp: '2025-07-10T16:02:36.385Z', actionType: 'negotiation', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: negotiation should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 3
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'PENDING_AGREEMENT', newStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
payerNegotiation.ts:348 [PayerNegotiationStore] Initializing socket listeners with centralized socket manager
payerNegotiation.ts:289 [PayerNegotiationStore] Setting up socket listeners with centralized socket manager
payerNegotiation.ts:134 [PayerNegotiationStore] fetchNegotiation called with transactionId: cmcxkt7ub0006vlp0kk66k1l6
payerNegotiation.ts:144 [PayerNegotiationStore] Fetching negotiation details from: /transactions/cmcxkt7ub0006vlp0kk66k1l6/payer-negotiation
payerNegotiation.ts:348 [PayerNegotiationStore] Initializing socket listeners with centralized socket manager
payerNegotiation.ts:289 [PayerNegotiationStore] Setting up socket listeners with centralized socket manager
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'negotiation', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: negotiation isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
prepare.js:1 🍍 "payerNegotiation" store installed 🆕
auth.ts:260 [AuthStore] Token expiry check: 1439 minutes until expiration
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktmgi000evlp0sqcfmfh9', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:36.402Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktmgi000evlp0sqcfmfh9",\n  "c…kk66k1l6",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktmgi000evlp0sqcfmfh9', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:36.402Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktmgi000evlp0sqcfmfh9",\n  "type": "…Provided",\n  "data": {\n    "username": "h2"\n  }\n}'}
payerNegotiation.ts:148 [PayerNegotiationStore] API Response Data: {data: {…}}
payerNegotiation.ts:154 [PayerNegotiationStore] Attempting to set currentNegotiation to: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:156 [PayerNegotiationStore] currentNegotiation after set: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:228 [PayerNegotiationStore] acceptCurrentProposal: Attempting for txId: cmcxkt7ub0006vlp0kk66k1l6
auth.ts:260 [AuthStore] Token expiry check: 1439 minutes until expiration
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktpw5000gvlp01cc2wdvh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:40.853Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktpw5000gvlp01cc2wdvh",\n  "c…0kk66k1l6",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktpw5000gvlp01cc2wdvh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:40.853Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktpw5000gvlp01cc2wdvh",\n  "type": "…al.agreed",\n  "data": {\n    "username": "h"\n  }\n}'}
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxkt1ct0001vlp0wkhqn6vx interests to READY_TO_NEGOTIATE
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:233 [PayerNegotiationStore] acceptCurrentProposal: Full API Response object: {
  "data": {
    "data": {
      "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
      "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
      "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
      "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
      "partyA_receivingInfoStatus": "PROVIDED",
      "partyB_receivingInfoStatus": "PROVIDED",
      "partyA_PaymentReceivingInfo": {
        "id": "cmckmrod00008vlv4gmcjhuqp",
        "bankName": "bankiran",
        "accountNumber": "1111",
        "accountHolderName": "h2",
        "isDefaultForUser": true
      },
      "partyB_PaymentReceivingInfo": {
        "id": "cmcknhjkd0001vlq84d32bu7q",
        "bankName": "bankanada",
        "accountNumber": "1111",
        "accountHolderName": "H",
        "isDefaultForUser": true
      },
      "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
      "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
      "systemRecommendationRule": "REPUTATION",
      "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
      "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
      "currentProposal_ById": "system",
      "currentProposal_Message": null,
      "partyA_agreedToCurrentProposal": false,
      "partyB_agreedToCurrentProposal": true,
      "negotiationStatus": "READY_TO_NEGOTIATE",
      "finalizedPayerId": null,
      "paymentTimerDueDate": null,
      "createdAt": "2025-07-10T16:02:31.987Z",
      "updatedAt": "2025-07-10T16:02:40.812Z",
      "isFinalOffer": false
    }
  },
  "status": 200,
  "statusText": "OK",
  "headers": {
    "content-length": "1215",
    "content-type": "application/json"
  },
  "config": {
    "transitional": {
      "silentJSONParsing": true,
      "forcedJSONParsing": true,
      "clarifyTimeoutError": false
    },
    "adapter": [
      "xhr",
      "http",
      "fetch"
    ],
    "transformRequest": [
      null
    ],
    "transformResponse": [
      null
    ],
    "timeout": 0,
    "xsrfCookieName": "XSRF-TOKEN",
    "xsrfHeaderName": "X-XSRF-TOKEN",
    "maxContentLength": -1,
    "maxBodyLength": -1,
    "env": {},
    "headers": {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.j1eXx1zOzTHF2nUfaIWjGWdHDaTyQ8uC2fTH87zwBFk"
    },
    "baseURL": "http://localhost:3000/api",
    "method": "post",
    "url": "/transactions/cmcxkt7ub0006vlp0kk66k1l6/payer-negotiation/agree",
    "allowAbsoluteUrls": true
  },
  "request": {}
}
payerNegotiation.ts:234 [PayerNegotiationStore] acceptCurrentProposal: API response.data: {
  "data": {
    "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
    "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
    "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
    "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
    "partyA_receivingInfoStatus": "PROVIDED",
    "partyB_receivingInfoStatus": "PROVIDED",
    "partyA_PaymentReceivingInfo": {
      "id": "cmckmrod00008vlv4gmcjhuqp",
      "bankName": "bankiran",
      "accountNumber": "1111",
      "accountHolderName": "h2",
      "isDefaultForUser": true
    },
    "partyB_PaymentReceivingInfo": {
      "id": "cmcknhjkd0001vlq84d32bu7q",
      "bankName": "bankanada",
      "accountNumber": "1111",
      "accountHolderName": "H",
      "isDefaultForUser": true
    },
    "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
    "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
    "systemRecommendationRule": "REPUTATION",
    "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
    "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
    "currentProposal_ById": "system",
    "currentProposal_Message": null,
    "partyA_agreedToCurrentProposal": false,
    "partyB_agreedToCurrentProposal": true,
    "negotiationStatus": "READY_TO_NEGOTIATE",
    "finalizedPayerId": null,
    "paymentTimerDueDate": null,
    "createdAt": "2025-07-10T16:02:31.987Z",
    "updatedAt": "2025-07-10T16:02:40.812Z",
    "isFinalOffer": false
  }
}
payerNegotiation.ts:235 [PayerNegotiationStore] acceptCurrentProposal: API response.data.data (if exists): {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:40.812Z",
  "isFinalOffer": false
}
payerNegotiation.ts:238 [PayerNegotiationStore] acceptCurrentProposal: Value to be assigned to currentNegotiation: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:40.812Z",
  "isFinalOffer": false
}
payerNegotiation.ts:240 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation BEFORE set: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:40.812Z",
  "isFinalOffer": false
}
payerNegotiation.ts:242 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation AFTER set: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:40.812Z",
  "isFinalOffer": false
}
payerNegotiation.ts:258 [PayerNegotiationStore] acceptCurrentProposal: Finished for txId: cmcxkt7ub0006vlp0kk66k1l6. Loading: false
ActionCard.vue:275 Negotiation updated: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktsll000ivlp0jw50b4lb', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:44.362Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktsll000ivlp0jw50b4lb",\n  "c…kk66k1l6",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktsll000ivlp0jw50b4lb', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:44.362Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktsll000ivlp0jw50b4lb",\n  "type": "…l.agreed",\n  "data": {\n    "username": "h2"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '791706bf-0be1-4bed-83c2-65b64639cba1', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h2 agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '791706bf-0be1-4bed-83c2-65b64639cba1', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h2 agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"791706bf-0be1-4bed-83c2-65b64639cba1","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Proposal Agreement: h2 agreed to the payer proposal in your transaction.","isRead":false,"createdAt":"2025-07-10T16:02:44.366Z","updatedAt":"2025-07-10T16:02:44.366Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxktj1v0008vlp09yw3ae24\",\"agreedUserId\":\"cmckmpoq60001vlpgo82crgdt\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification 791706bf-0be1-4bed-83c2-65b64639cba1.
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to AWAITING_FIRST_PAYER_PAYMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktsoe000kvlp0fb6g1hjd', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:02:44.462Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktsoe000kvlp0fb6g1hjd",\n  "c…"h2",\n    "dueDate": "10/07/2025, 19:02:44"\n  }\n}', hasData: true, dataKeys: Array(2), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktsoe000kvlp0fb6g1hjd', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:02:44.462Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktsoe000kvlp0fb6g1hjd",\n  "type": "…"h2",\n    "dueDate": "10/07/2025, 19:02:44"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '19fb1297-3c59-43af-964b-74d23c638f64', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Both parties agreed. h2 will make the first paymen…ransaction cmcxkt7u, due by 10/07/2025, 19:02:44.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '19fb1297-3c59-43af-964b-74d23c638f64', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Both parties agreed. h2 will make the first paymen…ransaction cmcxkt7u, due by 10/07/2025, 19:02:44.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"19fb1297-3c59-43af-964b-74d23c638f64","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Both parties agreed. h2 will make the first payment for transaction cmcxkt7u, due by 10/07/2025, 19:02:44.","isRead":false,"createdAt":"2025-07-10T16:02:44.479Z","updatedAt":"2025-07-10T16:02:44.479Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification 19fb1297-3c59-43af-964b-74d23c638f64.
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '28a71290-39e6-4f48-aa0c-9fa77d16bbfb', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '28a71290-39e6-4f48-aa0c-9fa77d16bbfb', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"28a71290-39e6-4f48-aa0c-9fa77d16bbfb","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Payer Decision Finalized: The payer decision has been finalized. h2 will pay first in your transaction.","isRead":false,"createdAt":"2025-07-10T16:02:44.498Z","updatedAt":"2025-07-10T16:02:44.498Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxktj1v0008vlp09yw3ae24\",\"finalizedPayerId\":\"cmckmpoq60001vlpgo82crgdt\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification 28a71290-39e6-4f48-aa0c-9fa77d16bbfb.
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxkt1ct0001vlp0wkhqn6vx interests to FINALIZED
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 2
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 7
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_PAYMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', newStatus: 'AWAITING_FIRST_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makePayment', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_makePayment isUsersTurn: false
auth.ts:260 [AuthStore] Token expiry check: 1439 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makePayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makePayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 7
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to AWAITING_SECOND_PAYER_CONFIRMATION
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxku017000mvlp0iqgl32l8', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:02:53.995Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxku017000mvlp0iqgl32l8",\n  "c…"otherUser": "h",\n    "amount": "1,000 CAD"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxku017000mvlp0iqgl32l8', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:02:53.995Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxku017000mvlp0iqgl32l8",\n  "type": "…"otherUser": "h",\n    "amount": "1,000 CAD"\n  }\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 3
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 8
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_CONFIRMATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
transactionalChatStore.ts:340 🔍 [fetchTransaction] Setting timer for transaction: cmcxkt7ub0006vlp0kk66k1l6 seconds: 7190
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-confirmReceipt', type: 'actionCard', timestamp: '2025-07-10T16:02:54.185Z', actionType: 'confirmReceipt', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 8
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_PAYMENT', newStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmReceipt', isUsersTurn: true, shouldShow: true}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: confirmReceipt isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
auth.ts:260 [AuthStore] Token expiry check: 1439 minutes until expiration
transactionalChatApi.ts:201 🔄 API: Performing action: confirmReceipt for transaction: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to AWAITING_SECOND_PAYER_PAYMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxku7fc000ovlp0px3d2urt', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:03:03.576Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxku7fc000ovlp0px3d2urt",\n  "c…CAD",\n    "dueDate": "10/07/2025, 19:03:03"\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxku7fc000ovlp0px3d2urt', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:03:03.576Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxku7fc000ovlp0px3d2urt",\n  "type": "…CAD",\n    "dueDate": "10/07/2025, 19:03:03"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 4
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 9
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_PAYMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', newStatus: 'AWAITING_SECOND_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makeSecondPayment', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: makeSecondPayment isUsersTurn: true
auth.ts:260 [AuthStore] Token expiry check: 1438 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makeSecondPayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-makeSecondPayment', type: 'actionCard', timestamp: '2025-07-10T16:03:03.766Z', actionType: 'makeSecondPayment', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makeSecondPayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 9
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatApi.ts:201 🔄 API: Performing action: declareSecondPayerPayment for transaction: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to AWAITING_FIRST_PAYER_CONFIRMATION
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkuf8f000qvlp0k8aa8had', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:03:13.695Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkuf8f000qvlp0k8aa8had",\n  "c…otherUser": "h2",\n    "amount": "57.0M IRR"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkuf8f000qvlp0k8aa8had', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:03:13.695Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkuf8f000qvlp0k8aa8had",\n  "type": "…otherUser": "h2",\n    "amount": "57.0M IRR"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '677f946b-2285-40f0-8b2e-ac866a1f4ca8', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'You have declared payment for transaction cmcxkt7u. Waiting for h2 to confirm.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '677f946b-2285-40f0-8b2e-ac866a1f4ca8', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'You have declared payment for transaction cmcxkt7u. Waiting for h2 to confirm.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"677f946b-2285-40f0-8b2e-ac866a1f4ca8","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"You have declared payment for transaction cmcxkt7u. Waiting for h2 to confirm.","isRead":false,"createdAt":"2025-07-10T16:03:13.711Z","updatedAt":"2025-07-10T16:03:13.711Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification 677f946b-2285-40f0-8b2e-ac866a1f4ca8.
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 5
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 10
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_CONFIRMATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_PAYMENT', newStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
SmartPaymentDeclaredSection.vue:133 [DECLARED] Formatting amount {amount: 57000000, currency: 'IRR'}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmFirstPaymentReceipt', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_confirmFirstPaymentReceipt isUsersTurn: false
auth.ts:260 [AuthStore] Token expiry check: 1438 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmFirstPaymentReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmFirstPaymentReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 10
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxkt4f30003vlp0muqypoik to COMPLETED
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukfz000svlp0bm1ibel2', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:03:20.447Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukfz000svlp0bm1ibel2",\n  "c…   "amount": "57.0M IRR",\n    "dueDate": ""\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukfz000svlp0bm1ibel2', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:03:20.447Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukfz000svlp0bm1ibel2",\n  "type": "…   "amount": "57.0M IRR",\n    "dueDate": ""\n  }\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukga000uvlp0tm2d7b8u', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:03:20.458Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukga000uvlp0tm2d7b8u",\n  "c…nId": "cmcxkt7ub0006vlp0kk66k1l6",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukga000uvlp0tm2d7b8u', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:03:20.458Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukga000uvlp0tm2d7b8u",\n  "type": "…temMessages.transaction.complete",\n  "data": {}\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukgj000wvlp07xdc8llm', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.firstPaymentConfirmed', createdAt: '2025-07-10T16:03:20.467Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukgj000wvlp07xdc8llm",\n  "c…\n  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6"\n}', hasData: false, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukgj000wvlp07xdc8llm', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.firstPaymentConfirmed', createdAt: '2025-07-10T16:03:20.467Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukgj000wvlp07xdc8llm",\n  "type": "…sactionalChat.systemLogs.firstPaymentConfirmed"\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 6
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 12
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: COMPLETED ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: finalized
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: finalized should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 12
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', newStatus: 'COMPLETED', isCompleted: true, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TransactionView.vue:155 🎉 Transaction completed! Triggering confetti for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:119 🎉 Confetti triggered by transaction completion!
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 57000000, amountToReceive: 1000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'finalized', isUsersTurn: false, shouldShow: false}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}

User B frontend logs:
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:291  [myOffersStore] Offer not found for negotiation state update: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
overrideMethod @ hook.js:608
handleNegotiationStateUpdate @ myOffersStore.ts:291
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:597
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktj35000avlp0nhabq5zh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:32.034Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktj35000avlp0nhabq5zh",\n  "c…0kk66k1l6",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktj35000avlp0nhabq5zh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:32.034Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktj35000avlp0nhabq5zh",\n  "type": "…sProvided",\n  "data": {\n    "username": "h"\n  }\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 2
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-paymentInfo', type: 'actionCard', timestamp: '2025-07-10T16:02:32.204Z', actionType: 'paymentInfo', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 2
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}}
ActionCard.vue:68 🔍 ActionCard - Payment Methods Summary: {actionCardId: 'action-paymentInfo', filteredMethodsCount: 4, targetCurrency: 'IRR', actionType: 'paymentInfo', methods: Array(4), …}
SmartPaymentInfoSection.vue:135 🎯 SmartPaymentInfoSection - selectedMethod changed: {newMethod: Proxy(Object), oldMethod: Proxy(Object), selectedMethodId: 'cmckmrod00008vlv4gmcjhuqp'}
SmartPaymentInfoSection.vue:105 🔄 SmartPaymentInfoSection - currentMethod watcher: {newMethod: Proxy(Object), oldMethod: Proxy(Object), selectedMethodId: 'cmckmrod00008vlv4gmcjhuqp'}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
ActionCard.vue:254 🎯 ActionCard - handlePaymentInfoConfirmed called: {method: Proxy(Object), validationStatus: 'complete'}
transactionalChatApi.ts:201 🔄 API: Performing action: paymentInfo for transaction: cmcxkt7ub0006vlp0kk66k1l6
SmartPaymentInfoSection.vue:105 🔄 SmartPaymentInfoSection - currentMethod watcher: {newMethod: Proxy(Object), oldMethod: Proxy(Object), selectedMethodId: 'cmckmrod00008vlv4gmcjhuqp'}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktmet000cvlp05avy556x', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:02:36.341Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktmet000cvlp05avy556x",\n  "c…nId": "cmcxkt7ub0006vlp0kk66k1l6",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktmet000cvlp05avy556x', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:02:36.341Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktmet000cvlp05avy556x",\n  "type": "…Chat.systemLogs.readyToNegotiate",\n  "data": {}\n}'}
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 1
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 3
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_DESIGNATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: negotiation
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-negotiation', type: 'actionCard', timestamp: '2025-07-10T16:02:36.387Z', actionType: 'negotiation', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: negotiation should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 3
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'PENDING_AGREEMENT', newStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
SmartPaymentInfoSection.vue:363 🔄 SmartPaymentInfoSection unmounted
payerNegotiation.ts:134 [PayerNegotiationStore] fetchNegotiation called with transactionId: cmcxkt7ub0006vlp0kk66k1l6
payerNegotiation.ts:144 [PayerNegotiationStore] Fetching negotiation details from: /transactions/cmcxkt7ub0006vlp0kk66k1l6/payer-negotiation
payerNegotiation.ts:348 [PayerNegotiationStore] Initializing socket listeners with centralized socket manager
payerNegotiation.ts:289 [PayerNegotiationStore] Setting up socket listeners with centralized socket manager
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'negotiation', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: negotiation isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktmgi000evlp0sqcfmfh9', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:36.402Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktmgi000evlp0sqcfmfh9",\n  "c…kk66k1l6",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktmgi000evlp0sqcfmfh9', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:02:36.402Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktmgi000evlp0sqcfmfh9",\n  "type": "…Provided",\n  "data": {\n    "username": "h2"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
payerNegotiation.ts:148 [PayerNegotiationStore] API Response Data: {data: {…}}
payerNegotiation.ts:154 [PayerNegotiationStore] Attempting to set currentNegotiation to: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:156 [PayerNegotiationStore] currentNegotiation after set: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
transactionalChatStore.ts:779 ⚠️ No socket update received within 800ms, performing manual refetch
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 1
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 4
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_DESIGNATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: negotiation
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-negotiation', type: 'actionCard', timestamp: '2025-07-10T16:02:37.369Z', actionType: 'negotiation', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: negotiation should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 4
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktpw5000gvlp01cc2wdvh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:40.853Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktpw5000gvlp01cc2wdvh",\n  "c…0kk66k1l6",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktpw5000gvlp01cc2wdvh', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:40.853Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktpw5000gvlp01cc2wdvh",\n  "type": "…al.agreed",\n  "data": {\n    "username": "h"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '09997722-816b-4134-bd29-fab56058a0e3', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '09997722-816b-4134-bd29-fab56058a0e3', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"09997722-816b-4134-bd29-fab56058a0e3","userId":"cmckmpoq60001vlpgo82crgdt","type":"TRANSACTION_UPDATE","message":"Proposal Agreement: h agreed to the payer proposal in your transaction.","isRead":false,"createdAt":"2025-07-10T16:02:40.862Z","updatedAt":"2025-07-10T16:02:40.862Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxktj1v0008vlp09yw3ae24\",\"agreedUserId\":\"cmckmpopk0000vlpgl7qffd1c\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification 09997722-816b-4134-bd29-fab56058a0e3.
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:291  [myOffersStore] Offer not found for negotiation state update: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
overrideMethod @ hook.js:608
handleNegotiationStateUpdate @ myOffersStore.ts:291
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:597
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:228 [PayerNegotiationStore] acceptCurrentProposal: Attempting for txId: cmcxkt7ub0006vlp0kk66k1l6
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktsll000ivlp0jw50b4lb', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:44.362Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktsll000ivlp0jw50b4lb",\n  "c…kk66k1l6",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktsll000ivlp0jw50b4lb', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:02:44.362Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktsll000ivlp0jw50b4lb",\n  "type": "…l.agreed",\n  "data": {\n    "username": "h2"\n  }\n}'}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxktsoe000kvlp0fb6g1hjd', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:02:44.462Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxktsoe000kvlp0fb6g1hjd",\n  "c…"h2",\n    "dueDate": "10/07/2025, 19:02:44"\n  }\n}', hasData: true, dataKeys: Array(2), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxktsoe000kvlp0fb6g1hjd', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:02:44.462Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxktsoe000kvlp0fb6g1hjd",\n  "type": "…"h2",\n    "dueDate": "10/07/2025, 19:02:44"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: 'f77d36ae-06b6-4e8c-86bf-07ad9ea4f763', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_ACTION_REQUIRED', message: 'You are the agreed first payer for transaction cmc…Please make your payment by 10/07/2025, 19:02:44.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: 'f77d36ae-06b6-4e8c-86bf-07ad9ea4f763', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_ACTION_REQUIRED', message: 'You are the agreed first payer for transaction cmc…Please make your payment by 10/07/2025, 19:02:44.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"f77d36ae-06b6-4e8c-86bf-07ad9ea4f763","userId":"cmckmpoq60001vlpgo82crgdt","type":"TRANSACTION_ACTION_REQUIRED","message":"You are the agreed first payer for transaction cmcxkt7u. Please make your payment by 10/07/2025, 19:02:44.","isRead":false,"createdAt":"2025-07-10T16:02:44.472Z","updatedAt":"2025-07-10T16:02:44.472Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification f77d36ae-06b6-4e8c-86bf-07ad9ea4f763.
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '3862e205-3b0f-4208-8706-d82931ab8c83', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '3862e205-3b0f-4208-8706-d82931ab8c83', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"3862e205-3b0f-4208-8706-d82931ab8c83","userId":"cmckmpoq60001vlpgo82crgdt","type":"TRANSACTION_UPDATE","message":"Payer Decision Finalized: The payer decision has been finalized. h2 will pay first in your transaction.","isRead":false,"createdAt":"2025-07-10T16:02:44.492Z","updatedAt":"2025-07-10T16:02:44.492Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxktj1v0008vlp09yw3ae24\",\"finalizedPayerId\":\"cmckmpoq60001vlpgo82crgdt\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification 3862e205-3b0f-4208-8706-d82931ab8c83.
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:291  [myOffersStore] Offer not found for negotiation state update: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
overrideMethod @ hook.js:608
handleNegotiationStateUpdate @ myOffersStore.ts:291
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:597
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:233 [PayerNegotiationStore] acceptCurrentProposal: Full API Response object: {
  "data": {
    "data": {
      "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
      "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
      "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
      "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
      "partyA_receivingInfoStatus": "PROVIDED",
      "partyB_receivingInfoStatus": "PROVIDED",
      "partyA_PaymentReceivingInfo": {
        "id": "cmckmrod00008vlv4gmcjhuqp",
        "bankName": "bankiran",
        "accountNumber": "1111",
        "accountHolderName": "h2",
        "isDefaultForUser": true
      },
      "partyB_PaymentReceivingInfo": {
        "id": "cmcknhjkd0001vlq84d32bu7q",
        "bankName": "bankanada",
        "accountNumber": "1111",
        "accountHolderName": "H",
        "isDefaultForUser": true
      },
      "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
      "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
      "systemRecommendationRule": "REPUTATION",
      "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
      "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
      "currentProposal_ById": "system",
      "currentProposal_Message": null,
      "partyA_agreedToCurrentProposal": true,
      "partyB_agreedToCurrentProposal": true,
      "negotiationStatus": "FINALIZED",
      "finalizedPayerId": "cmckmpoq60001vlpgo82crgdt",
      "paymentTimerDueDate": null,
      "createdAt": "2025-07-10T16:02:31.987Z",
      "updatedAt": "2025-07-10T16:02:44.372Z",
      "isFinalOffer": false
    }
  },
  "status": 200,
  "statusText": "OK",
  "headers": {
    "content-length": "1228",
    "content-type": "application/json"
  },
  "config": {
    "transitional": {
      "silentJSONParsing": true,
      "forcedJSONParsing": true,
      "clarifyTimeoutError": false
    },
    "adapter": [
      "xhr",
      "http",
      "fetch"
    ],
    "transformRequest": [
      null
    ],
    "transformResponse": [
      null
    ],
    "timeout": 0,
    "xsrfCookieName": "XSRF-TOKEN",
    "xsrfHeaderName": "X-XSRF-TOKEN",
    "maxContentLength": -1,
    "maxBodyLength": -1,
    "env": {},
    "headers": {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.hOn1eblnZeY_rjoV6X2FGOPfbd-ltf1Xka4MMyoq47c"
    },
    "baseURL": "http://localhost:3000/api",
    "method": "post",
    "url": "/transactions/cmcxkt7ub0006vlp0kk66k1l6/payer-negotiation/agree",
    "allowAbsoluteUrls": true
  },
  "request": {}
}
payerNegotiation.ts:234 [PayerNegotiationStore] acceptCurrentProposal: API response.data: {
  "data": {
    "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
    "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
    "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
    "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
    "partyA_receivingInfoStatus": "PROVIDED",
    "partyB_receivingInfoStatus": "PROVIDED",
    "partyA_PaymentReceivingInfo": {
      "id": "cmckmrod00008vlv4gmcjhuqp",
      "bankName": "bankiran",
      "accountNumber": "1111",
      "accountHolderName": "h2",
      "isDefaultForUser": true
    },
    "partyB_PaymentReceivingInfo": {
      "id": "cmcknhjkd0001vlq84d32bu7q",
      "bankName": "bankanada",
      "accountNumber": "1111",
      "accountHolderName": "H",
      "isDefaultForUser": true
    },
    "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
    "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
    "systemRecommendationRule": "REPUTATION",
    "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
    "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
    "currentProposal_ById": "system",
    "currentProposal_Message": null,
    "partyA_agreedToCurrentProposal": true,
    "partyB_agreedToCurrentProposal": true,
    "negotiationStatus": "FINALIZED",
    "finalizedPayerId": "cmckmpoq60001vlpgo82crgdt",
    "paymentTimerDueDate": null,
    "createdAt": "2025-07-10T16:02:31.987Z",
    "updatedAt": "2025-07-10T16:02:44.372Z",
    "isFinalOffer": false
  }
}
payerNegotiation.ts:235 [PayerNegotiationStore] acceptCurrentProposal: API response.data.data (if exists): {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": true,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "FINALIZED",
  "finalizedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:44.372Z",
  "isFinalOffer": false
}
payerNegotiation.ts:238 [PayerNegotiationStore] acceptCurrentProposal: Value to be assigned to currentNegotiation: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": true,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "FINALIZED",
  "finalizedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:44.372Z",
  "isFinalOffer": false
}
payerNegotiation.ts:240 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation BEFORE set: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": true,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "FINALIZED",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:44.372Z",
  "isFinalOffer": false
}
payerNegotiation.ts:242 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation AFTER set: {
  "negotiationId": "cmcxktj1v0008vlp09yw3ae24",
  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": true,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "FINALIZED",
  "finalizedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:02:31.987Z",
  "updatedAt": "2025-07-10T16:02:44.372Z",
  "isFinalOffer": false
}
payerNegotiation.ts:258 [PayerNegotiationStore] acceptCurrentProposal: Finished for txId: cmcxkt7ub0006vlp0kk66k1l6. Loading: false
ActionCard.vue:275 Negotiation updated: Proxy(Object) {negotiationId: 'cmcxktj1v0008vlp09yw3ae24', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 2
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 7
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_PAYMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', newStatus: 'AWAITING_FIRST_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makePayment', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: makePayment isUsersTurn: true
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makePayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-makePayment', type: 'actionCard', timestamp: '2025-07-10T16:02:44.630Z', actionType: 'makePayment', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makePayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 7
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatApi.ts:201 🔄 API: Performing action: declareFirstPayerPayment for transaction: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxku017000mvlp0iqgl32l8', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:02:53.995Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxku017000mvlp0iqgl32l8",\n  "c…"otherUser": "h",\n    "amount": "1,000 CAD"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxku017000mvlp0iqgl32l8', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:02:53.995Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxku017000mvlp0iqgl32l8",\n  "type": "…"otherUser": "h",\n    "amount": "1,000 CAD"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 3
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 8
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_CONFIRMATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
transactionalChatStore.ts:340 🔍 [fetchTransaction] Setting timer for transaction: cmcxkt7ub0006vlp0kk66k1l6 seconds: 7190
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 8
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_PAYMENT', newStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
SmartPaymentDeclaredSection.vue:133 [DECLARED] Formatting amount {amount: 1000, currency: 'CAD'}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmReceipt', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_confirmReceipt isUsersTurn: false
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxku7fc000ovlp0px3d2urt', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:03:03.576Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxku7fc000ovlp0px3d2urt",\n  "c…CAD",\n    "dueDate": "10/07/2025, 19:03:03"\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxku7fc000ovlp0px3d2urt', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:03:03.576Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxku7fc000ovlp0px3d2urt",\n  "type": "…CAD",\n    "dueDate": "10/07/2025, 19:03:03"\n  }\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 4
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 9
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_PAYMENT ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', newStatus: 'AWAITING_SECOND_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makeSecondPayment', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_makeSecondPayment isUsersTurn: false
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makeSecondPayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makeSecondPayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 9
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkuf8f000qvlp0k8aa8had', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:03:13.695Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkuf8f000qvlp0k8aa8had",\n  "c…otherUser": "h2",\n    "amount": "57.0M IRR"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkuf8f000qvlp0k8aa8had', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:03:13.695Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkuf8f000qvlp0k8aa8had",\n  "type": "…otherUser": "h2",\n    "amount": "57.0M IRR"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: 'f4552bda-6cd3-4275-9062-0bb008842710', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_ACTION_REQUIRED', message: 'h has declared payment for transaction cmcxkt7u. Please confirm receipt.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: 'f4552bda-6cd3-4275-9062-0bb008842710', userId: 'cmckmpoq60001vlpgo82crgdt', type: 'TRANSACTION_ACTION_REQUIRED', message: 'h has declared payment for transaction cmcxkt7u. Please confirm receipt.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"f4552bda-6cd3-4275-9062-0bb008842710","userId":"cmckmpoq60001vlpgo82crgdt","type":"TRANSACTION_ACTION_REQUIRED","message":"h has declared payment for transaction cmcxkt7u. Please confirm receipt.","isRead":false,"createdAt":"2025-07-10T16:03:13.702Z","updatedAt":"2025-07-10T16:03:13.702Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxkt7ub0006vlp0kk66k1l6","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification f4552bda-6cd3-4275-9062-0bb008842710.
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 5
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 10
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_CONFIRMATION ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_PAYMENT', newStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmFirstPaymentReceipt', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: confirmFirstPaymentReceipt isUsersTurn: true
auth.ts:260 [AuthStore] Token expiry check: 1392 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmFirstPaymentReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-confirmFirstPaymentReceipt', type: 'actionCard', timestamp: '2025-07-10T16:03:13.873Z', actionType: 'confirmFirstPaymentReceipt', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmFirstPaymentReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 10
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatApi.ts:201 🔄 API: Performing action: confirmFirstPayerPayment for transaction: cmcxkt7ub0006vlp0kk66k1l6
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxkt7ub0006vlp0kk66k1l6', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', offerId: 'cmcxkt1ct0001vlp0wkhqn6vx', status: 'COMPLETED', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukfz000svlp0bm1ibel2', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:03:20.447Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukfz000svlp0bm1ibel2",\n  "c…   "amount": "57.0M IRR",\n    "dueDate": ""\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukfz000svlp0bm1ibel2', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:03:20.447Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukfz000svlp0bm1ibel2",\n  "type": "…   "amount": "57.0M IRR",\n    "dueDate": ""\n  }\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukga000uvlp0tm2d7b8u', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:03:20.458Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukga000uvlp0tm2d7b8u",\n  "c…nId": "cmcxkt7ub0006vlp0kk66k1l6",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukga000uvlp0tm2d7b8u', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:03:20.458Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukga000uvlp0tm2d7b8u",\n  "type": "…temMessages.transaction.complete",\n  "data": {}\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxkukgj000wvlp07xdc8llm', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.firstPaymentConfirmed', createdAt: '2025-07-10T16:03:20.467Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxkukgj000wvlp07xdc8llm",\n  "c…\n  "transactionId": "cmcxkt7ub0006vlp0kk66k1l6"\n}', hasData: false, dataKeys: Array(0), currentChatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxkukgj000wvlp07xdc8llm', chatSessionId: 'cmcxkt7t50005vlp0ebdrwwfg', content: 'transactionalChat.systemLogs.firstPaymentConfirmed', createdAt: '2025-07-10T16:03:20.467Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxkukgj000wvlp07xdc8llm",\n  "type": "…sactionalChat.systemLogs.firstPaymentConfirmed"\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxkt7ub0006vlp0kk66k1l6 (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 6
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 12
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpopk0000vlpgl7qffd1c', name: 'h', profilePic: undefined, reputation: 5}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: COMPLETED ID: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxkt7ub0006vlp0kk66k1l6
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: finalized
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: finalized should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 12
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', newStatus: 'COMPLETED', isCompleted: true, completedEnum: 'COMPLETED', transactionId: 'cmcxkt7ub0006vlp0kk66k1l6'}
TransactionView.vue:155 🎉 Transaction completed! Triggering confetti for transaction: cmcxkt7ub0006vlp0kk66k1l6
TransactionView.vue:119 🎉 Confetti triggered by transaction completion!
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 1000, amountToReceive: 57000000, currencyFrom: 'CAD', currencyTo: 'IRR', isUserFirstPayer: true, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'finalized', isUsersTurn: false, shouldShow: false}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}



User A list of system log messages:
ℹ️
جزئیات پرداخت توسط h ارسال شد.

17:02
ℹ️
جمع‌آوری اطلاعات پرداخت تکمیل شد. مرحله تعیین پرداخت‌کننده اول آغاز شد.

17:02
ℹ️
جزئیات پرداخت توسط h2 ارسال شد.

17:02
ℹ️
پیشنهاد توسط h پذیرفته شد.

17:02
ℹ️
پیشنهاد توسط h2 پذیرفته شد.

17:02
ℹ️
توافق متقابل حاصل شد. h2 به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: 10/07/2025, 19:02:44.

17:02
ℹ️
اعلام پرداخت توسط h2 برای مبلغ 1,000 CAD ثبت شد. تأیید دریافت از h در انتظار است.

17:02
ℹ️
تأیید دریافت پرداخت اول توسط h انجام شد. مرحله پرداخت دوم برای h2 به h آغاز شد. مهلت پرداخت: 10/07/2025, 19:03:03.

17:03
ℹ️
اعلام پرداخت توسط h برای مبلغ 57.0M IRR ثبت شد. تأیید دریافت از h2 در انتظار است.

17:03
ℹ️
تأیید دریافت پرداخت نهایی توسط h2 انجام شد. معامله تکمیل شد.

17:03
🎉
معامله با موفقیت تکمیل شد. 🎉

17:03
ℹ️
تأیید دریافت پرداخت اول توسط انجام شد.

17:03


system logs after refresh:
جزئیات پرداخت توسط h ارسال شد.

17:02
ℹ️
جمع‌آوری اطلاعات پرداخت تکمیل شد. مرحله تعیین پرداخت‌کننده اول آغاز شد.

17:02
ℹ️
جزئیات پرداخت توسط h2 ارسال شد.

17:02
ℹ️
پیشنهاد توسط h2 پذیرفته شد.

17:02
ℹ️
پیشنهاد توسط h2 پذیرفته شد.

17:02
ℹ️
توافق متقابل حاصل شد. h2 به عنوان پرداخت‌کننده اول تعیین شد. مهلت پرداخت: 10/07/2025, 19:02:44.

17:02
ℹ️
اعلام پرداخت توسط h2 برای مبلغ 1,000 CAD ثبت شد. تأیید دریافت از h در انتظار است.

17:02
ℹ️
تأیید دریافت پرداخت اول توسط h انجام شد. مرحله پرداخت دوم برای h2 به h آغاز شد. مهلت پرداخت: 10/07/2025, 19:03:03.

17:03
ℹ️
اعلام پرداخت توسط h برای مبلغ 57.0M IRR ثبت شد. تأیید دریافت از h2 در انتظار است.

17:03
ℹ️
تأیید دریافت پرداخت نهایی توسط h2 انجام شد. معامله تکمیل شد.

17:03
🎉
معامله با موفقیت تکمیل شد. 🎉

17:03
ℹ️
تأیید دریافت پرداخت اول توسط h انجام شد.

17:03