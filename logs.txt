backend:
   '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 200 56ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 204 0ms
<-- OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
--> OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 204 1ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
[TransactionalChatRoutes] GET /:transactionId/timer - cmcxml27z0006vlqkbu4pl9vm for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
<-- GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
[TransactionService] Initialized with IO, NotificationService, ChatService, and optional MatchingService
[TransactionService] Fetched transaction by chatSessionId: cmcxml2740005vlqk1cln5zpz (TX ID: cmcxml27z0006vlqkbu4pl9vm) for user cmckmpopk0000vlpgl7qffd1c
--> GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 200 18ms
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 200 62ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions 204 0ms
<-- POST /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions
[TransactionalChatRoutes] POST /:transactionId/actions - declareSecondPayerPayment for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Performing action declareSecondPayerPayment for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
[TransactionService] User cmckmpopk0000vlpgl7qffd1c declaring payment for transaction cmcxml27z0006vlqkbu4pl9vm
[TransactionService] DEBUG: Emitting payload with usernames - A: "h2", B: "h"
[TransactionService] Emitted TRANSACTION_STATUS_UPDATED for transaction cmcxml27z0006vlqkbu4pl9vm to user rooms cmckmpopk0000vlpgl7qffd1c and cmckmpoq60001vlpgo82crgdt
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxml2740005vlqk1cln5zpz',
  messageContent: 'systemMessages.payment.declared',
  transactionId: 'cmcxml27z0006vlqkbu4pl9vm',
  messageData: '{\n  "username": "h",\n  "otherUser": "h2",\n  "amount": "285.0M IRR"\n}'     
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxmmku2000qvlqkapug4kfy",\n' +
    '  "chatSessionId": "cmcxml2740005vlqk1cln5zpz",\n' +
    '  "content": "systemMessages.payment.declared",\n' +
    '  "createdAt": "2025-07-10T16:53:06.939Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",\n' +
    '  "data": {\n' +
    '    "username": "h",\n' +
    '    "otherUser": "h2",\n' +
    '    "amount": "285.0M IRR"\n' +
    '  }\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxml27z0006vlqkbu4pl9vm
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxml2740005vlqk1cln5zpz, transaction cmcxml27z0006vlqkbu4pl9vm: systemMessages.payment.declared
[NotificationService] Emitted NEW_NOTIFICATION to userId: cmckmpoq60001vlpgo82crgdt
[NotificationService] Notification created: 6569d75e-1165-45f5-a273-d9def85ab216 for user cmckmpoq60001vlpgo82crgdt
[NotificationService] Emitted NEW_NOTIFICATION to userId: cmckmpopk0000vlpgl7qffd1c
[NotificationService] Notification created: dc8ae3bc-5bfd-4a7d-958b-a74cdbb4e06d for user cmckmpopk0000vlpgl7qffd1c
--> POST /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions 200 86ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 204 1ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 204 1ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
[TransactionalChatRoutes] GET /:transactionId - cmcxml27z0006vlqkbu4pl9vm for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
[TransactionalChatRoutes] GET /:transactionId - cmcxml27z0006vlqkbu4pl9vm for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpoq60001vlpgo82crgdt
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 200 59ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 200 59ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 204 1ms
<-- OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
--> OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 204 0ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
[TransactionalChatRoutes] GET /:transactionId/timer - cmcxml27z0006vlqkbu4pl9vm for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpoq60001vlpgo82crgdt
<-- GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
[TransactionService] Initialized with IO, NotificationService, ChatService, and optional MatchingService
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 204 1ms
<-- OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
--> OPTIONS /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 204 1ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer
[TransactionalChatRoutes] GET /:transactionId/timer - cmcxml27z0006vlqkbu4pl9vm for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
[TransactionService] Fetched transaction by chatSessionId: cmcxml2740005vlqk1cln5zpz (TX ID: cmcxml27z0006vlqkbu4pl9vm) for user cmckmpoq60001vlpgo82crgdt
--> GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 200 14ms
<-- GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz
[TransactionService] Initialized with IO, NotificationService, ChatService, and optional MatchingService
[TransactionService] Fetched transaction by chatSessionId: cmcxml2740005vlqk1cln5zpz (TX ID: cmcxml27z0006vlqkbu4pl9vm) for user cmckmpopk0000vlpgl7qffd1c
--> GET /api/transactions/chat/cmcxml2740005vlqk1cln5zpz 200 15ms
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 200 66ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/timer 200 70ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions 204 1ms
<-- POST /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions
[TransactionalChatRoutes] POST /:transactionId/actions - confirmFirstPayerPayment for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Performing action confirmFirstPayerPayment for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpoq60001vlpgo82crgdt
[TransactionService] User cmckmpoq60001vlpgo82crgdt confirming receipt for transaction cmcxml27z0006vlqkbu4pl9vm
[TransactionService] DEBUG: Emitting payload with usernames - A: "h2", B: "h"
[TransactionService] Emitted TRANSACTION_STATUS_UPDATED for transaction cmcxml27z0006vlqkbu4pl9vm to user rooms cmckmpopk0000vlpgl7qffd1c and cmckmpoq60001vlpgo82crgdt
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxml2740005vlqk1cln5zpz',
  messageContent: 'systemMessages.payment.confirmedSecond',
  transactionId: 'cmcxml27z0006vlqkbu4pl9vm',
  messageData: '{\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2",\n' +
    '  "amount": "285.0M IRR",\n' +
    '  "dueDate": ""\n' +
    '}'
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxmmoa6000svlqkzumd9kmv',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:53:11.406Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxmmoa6000svlqkzumd9kmv",\n' +
    '  "chatSessionId": "cmcxml2740005vlqk1cln5zpz",\n' +
    '  "content": "systemMessages.payment.confirmedSecond",\n' +
    '  "createdAt": "2025-07-10T16:53:11.406Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",\n' +
    '  "data": {\n' +
    '    "username": "h2",\n' +
    '    "payerUser": "h",\n' +
    '    "receiverUser": "h2",\n' +
    '    "amount": "285.0M IRR",\n' +
    '    "dueDate": ""\n' +
    '  }\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxml27z0006vlqkbu4pl9vm
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxml2740005vlqk1cln5zpz, transaction cmcxml27z0006vlqkbu4pl9vm: systemMessages.payment.confirmedSecond
🔍 [TransactionService] createAndEmitSystemMessage called: {
  chatSessionId: 'cmcxml2740005vlqk1cln5zpz',
  messageContent: 'systemMessages.transaction.complete',
  transactionId: 'cmcxml27z0006vlqkbu4pl9vm',
  messageData: '{}'
}
🔍 [TransactionService] Created database message: {
  messageId: 'cmcxmmoaj000uvlqkowva4q4d',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:53:11.419Z'
}
🔍 [TransactionService] About to emit SystemMessagePayload: {
  payload: '{\n' +
    '  "messageId": "cmcxmmoaj000uvlqkowva4q4d",\n' +
    '  "chatSessionId": "cmcxml2740005vlqk1cln5zpz",\n' +
    '  "content": "systemMessages.transaction.complete",\n' +
    '  "createdAt": "2025-07-10T16:53:11.419Z",\n' +
    '  "isSystemMessage": true,\n' +
    '  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",\n' +
    '  "data": {}\n' +
    '}'
}
🔍 [TransactionService] Emitting to userOneId: cmckmpopk0000vlpgl7qffd1c
🔍 [TransactionService] Emitting to userTwoId: cmckmpoq60001vlpgo82crgdt
✅ [TransactionService] System message emitted successfully for transaction: cmcxml27z0006vlqkbu4pl9vm
[TransactionService] System message saved to database and emitted via SYSTEM_MESSAGE_RECEIVE to participants of chat cmcxml2740005vlqk1cln5zpz, transaction cmcxml27z0006vlqkbu4pl9vm: systemMessages.transaction.complete
--> POST /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm/actions 200 64ms
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 204 1ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
[TransactionalChatRoutes] GET /:transactionId - cmcxml27z0006vlqkbu4pl9vm for user cmckmpopk0000vlpgl7qffd1c
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpopk0000vlpgl7qffd1c
<-- OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
--> OPTIONS /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 204 1ms
<-- GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm
[TransactionalChatRoutes] GET /:transactionId - cmcxml27z0006vlqkbu4pl9vm for user cmckmpoq60001vlpgo82crgdt
[TransactionalChatService] Getting chat details for transaction cmcxml27z0006vlqkbu4pl9vm, user cmckmpoq60001vlpgo82crgdt
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:02.979Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxml7he000avlqkxjnj323s',
  messageData: '{\n  "username": "h"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  content: 'transactionalChat.systemLogs.readyToNegotiate',
  createdAt: '2025-07-10T16:52:09.564Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmlckb000cvlqk3sdmj1mw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: transactionalChat.systemLogs.readyToNegotiate
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmoa6000svlqkzumd9kmv',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:53:11.406Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmoa6000svlqkzumd9kmv',
  messageData: '{\n' +
    '  "amount": "285.0M IRR",\n' +
    '  "dueDate": "",\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmoaj000uvlqkowva4q4d',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:53:11.419Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmmoaj000uvlqkowva4q4d, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.transaction.complete
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlckb000cvlqk3sdmj1mw',
  messageData: '{}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  content: 'transactionalChat.systemLogs.paymentDetailsProvided',
  createdAt: '2025-07-10T16:52:09.586Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmlcky000evlqkilccksa4',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:45.423Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm48f000gvlqkwd2qb5nw, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmoaj000uvlqkowva4q4d',
  messageData: '{}',
  wasReconstructed: true
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 200 49ms
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm48f000gvlqkwd2qb5nw',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  content: 'systemMessages.proposal.agreed',
  createdAt: '2025-07-10T16:52:46.690Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmm57l000ivlqk8zeqphd8, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.proposal.agreed 
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm57l000ivlqk8zeqphd8',
  messageData: '{\n  "username": "h2"\n}',
  wasReconstructed: true
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  content: 'systemMessages.proposal.bothAgreed',
  createdAt: '2025-07-10T16:52:46.805Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmm5at000kvlqkmv5cfnga',
  messageData: '{\n  "dueDate": "10/07/2025, 19:52:46",\n  "firstPayer": "h2"\n}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:52:55.188Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmbro000mvlqkbofl9l44',
  messageData: '{\n  "amount": "5,000 CAD",\n  "username": "h2",\n  "otherUser": "h"\n}',     
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  content: 'systemMessages.payment.confirmedFirst',
  createdAt: '2025-07-10T16:53:01.370Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmgje000ovlqk92tkajrr',
  messageData: '{\n' +
    '  "amount": "5,000 CAD",\n' +
    '  "dueDate": "10/07/2025, 19:53:01",\n' +
    '  "username": "h",\n' +
    '  "payerUser": "h2",\n' +
    '  "receiverUser": "h"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  content: 'systemMessages.payment.declared',
  createdAt: '2025-07-10T16:53:06.939Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmku2000qvlqkapug4kfy',
  messageData: '{\n  "amount": "285.0M IRR",\n  "username": "h",\n  "otherUser": "h2"\n}',    
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmoa6000svlqkzumd9kmv',
  content: 'systemMessages.payment.confirmedSecond',
  createdAt: '2025-07-10T16:53:11.406Z'
}
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmoa6000svlqkzumd9kmv',
  messageData: '{\n' +
    '  "amount": "285.0M IRR",\n' +
    '  "dueDate": "",\n' +
    '  "username": "h2",\n' +
    '  "payerUser": "h",\n' +
    '  "receiverUser": "h2"\n' +
    '}',
  wasReconstructed: false
}
🔍 [TransactionalChatService] Processing historical system message: {
  messageId: 'cmcxmmoaj000uvlqkowva4q4d',
  content: 'systemMessages.transaction.complete',
  createdAt: '2025-07-10T16:53:11.419Z'
}
🔍 [TransactionalChatService] No stored data found for message cmcxmmoaj000uvlqkowva4q4d, reconstructing...
🔍 [TransactionalChatService] Reconstructing data for message: systemMessages.transaction.complete
🔍 [TransactionalChatService] Using data for historical message: {
  messageId: 'cmcxmmoaj000uvlqkowva4q4d',
  messageData: '{}',
  wasReconstructed: true
}
--> GET /api/transactional-chat/cmcxml27z0006vlqkbu4pl9vm 200 58ms


frontend:
useClientLogger.ts:67 [INFO] Route navigation started {from: '/my-offers', to: '/create-offer', routeName: 'CreateOffer', requiresAuth: true, requiresPhoneVerified: true}
useClientLogger.ts:67 [INFO] User action: navigation {to: '/create-offer', from: '/my-offers', timestamp: '2025-07-10T16:51:42.314Z'}
useClientLogger.ts:67 [INFO] Route navigation completed {from: '/my-offers', to: '/create-offer', routeName: 'CreateOffer'}
prepare.js:1 🍍 "uiPreferences" store installed 🆕
useClientLogger.ts:67 [INFO] Creating new offer {currencyPair: 'CAD-IRR', amount: 5000}
auth.ts:260 [AuthStore] Token expiry check: 1390 minutes until expiration
centralizedSocketManager.ts:565 🔔 [CentralizedSocketManager] Received OFFER_CREATED: {offerId: 'cmcxmkvts0001vlqk627e47mh', userId: 'cmckmpopk0000vlpgl7qffd1c', fullOfferData: {…}}
myOffersStore.ts:326 🔥 [myOffersStore] Handling OFFER_CREATED event: {offerId: 'cmcxmkvts0001vlqk627e47mh', userId: 'cmckmpopk0000vlpgl7qffd1c', fullOfferData: {…}}
myOffersStore.ts:339 [myOffersStore] New offer created, re-fetching offers to include it
myOffersStore.ts:343 [myOffersStore] Adding new offer directly to local state: {id: 'cmcxmkvts0001vlqk627e47mh', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'BUY', currencyPair: 'CAD-IRR', amount: 5000, …}
myOffersStore.ts:347 [myOffersStore] New offer added to local state
myOffersStore.ts:104 [myOffersStore] Fetching my offers with includeInterests=1
auth.ts:260 [AuthStore] Token expiry check: 1390 minutes until expiration
useClientLogger.ts:67 [INFO] Offer created successfully {offerId: 'cmcxmkvts0001vlqk627e47mh'}
useClientLogger.ts:67 [INFO] Route navigation started {from: '/create-offer', to: '/my-offers', routeName: 'MyOffers', requiresAuth: true, requiresPhoneVerified: false}
useClientLogger.ts:67 [INFO] User action: navigation {to: '/my-offers', from: '/create-offer', timestamp: '2025-07-10T16:51:47.962Z'}
useClientLogger.ts:67 [INFO] Route navigation completed {from: '/create-offer', to: '/my-offers', routeName: 'MyOffers'}
MyOffersView.vue:172 🔥 [MyOffersView] About to call useMyOffersStore()
MyOffersView.vue:174 🔥 [MyOffersView] myOffersStore created: Proxy(Object) {$id: 'myOffers', $onAction: ƒ, $patch: ƒ, $reset: ƒ, $subscribe: ƒ, …}
myOffersStore.ts:104 [myOffersStore] Fetching my offers with includeInterests=1
auth.ts:260 [AuthStore] Token expiry check: 1390 minutes until expiration
myOffersStore.ts:106 [myOffersStore] Received response for /offers/my: (60) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
myOffersStore.ts:110 [myOffersStore] Mapped myOffers: (60) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
myOffersStore.ts:355 🔥 [myOffersStore] Successfully re-fetched offers after OFFER_CREATED
myOffersStore.ts:106 [myOffersStore] Received response for /offers/my: (60) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
myOffersStore.ts:110 [myOffersStore] Mapped myOffers: (60) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
centralizedSocketManager.ts:544 🔔 [CentralizedSocketManager] Received INTEREST_RECEIVED: {interestId: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', offer: {…}, interestedUser: {…}}
myOffersStore.ts:296 🔥🔥🔥 [myOffersStore] Handling INTEREST_RECEIVED event: {interestId: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', offer: {…}, interestedUser: {…}}
myOffersStore.ts:195 [myOffersStore] New interest added to offer: cmcxmkvts0001vlqk627e47mh {id: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', interestedUserId: undefined, username: 'h2', reputationLevel: 3, …}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: 'a00a54a1-f3d5-4ba1-afdf-ce21a4b61cd4', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'NEW_INTEREST_ON_YOUR_OFFER', message: 'User h2 showed interest in your offer: "BUY 5000 CAD-IRR".', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: 'a00a54a1-f3d5-4ba1-afdf-ce21a4b61cd4', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'NEW_INTEREST_ON_YOUR_OFFER', message: 'User h2 showed interest in your offer: "BUY 5000 CAD-IRR".', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"a00a54a1-f3d5-4ba1-afdf-ce21a4b61cd4","userId":"cmckmpopk0000vlpgl7qffd1c","type":"NEW_INTEREST_ON_YOUR_OFFER","message":"User h2 showed interest in your offer: \"BUY 5000 CAD-IRR\".","isRead":false,"createdAt":"2025-07-10T16:51:52.095Z","updatedAt":"2025-07-10T16:51:52.095Z","relatedEntityType":"OFFER","relatedEntityId":"cmcxmkvts0001vlqk627e47mh","actorId":"cmckmpoq60001vlpgo82crgdt","actorUsername":"h2","data":"{\"interestId\":\"cmcxmkz270003vlqkci4rxj4j\",\"offerId\":\"cmcxmkvts0001vlqk627e47mh\",\"offerTitle\":\"BUY 5000 CAD-IRR\",\"offerType\":\"BUY\",\"offerAmount\":5000,\"currencyPair\":\"CAD-IRR\",\"interestedUserId\":\"cmckmpoq60001vlpgo82crgdt\",\"interestedUserUsername\":\"h2\",\"interestedUserReputation\":3,\"createdAt\":\"2025-07-10T16:51:52.063Z\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification a00a54a1-f3d5-4ba1-afdf-ce21a4b61cd4.
auth.ts:260 [AuthStore] Token expiry check: 1390 minutes until expiration
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:249  [myOffersStore] Offer not found for transaction status update: {chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
overrideMethod @ hook.js:608
handleTransactionStatusUpdate @ myOffersStore.ts:249
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:591
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
centralizedSocketManager.ts:554 🔔 [CentralizedSocketManager] Received INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY: {interestId: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offer: {…}, offerCreator: {…}, …}
myOffersStore.ts:437 🔥 [myOffersStore] HANDLER CALLED - INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY event: {interestId: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offer: {…}, offerCreator: {…}, …}
myOffersStore.ts:477 [myOffersStore] Interest status updated (by replacement) to ACCEPTED and chatSessionId set in offer: cmcxmkvts0001vlqk627e47mh {id: 'cmcxmkz270003vlqkci4rxj4j', offerId: 'cmcxmkvts0001vlqk627e47mh', interestedUserId: undefined, username: 'h2', reputationLevel: 3, …}
myOffersStore.ts:500  [myOffersStore] No NEW_INTEREST_ON_YOUR_OFFER notification found for accepted interest cmcxmkz270003vlqkci4rxj4j to remove/mark as read.
overrideMethod @ hook.js:608
handleInterestAcceptedAndChatReady @ myOffersStore.ts:500
(anonymous) @ centralizedSocketManager.ts:643
_notifyHandlers @ centralizedSocketManager.ts:641
(anonymous) @ centralizedSocketManager.ts:555
Emitter.emit @ index.js:136
emitEvent @ socket.js:538
onevent @ socket.js:525
onpacket @ socket.js:495
Emitter.emit @ index.js:136
(anonymous) @ manager.js:209
Promise.then
(anonymous) @ globals.js:4
ondecoded @ manager.js:208
Emitter.emit @ index.js:136
add @ index.js:146
ondata @ manager.js:195
Emitter.emit @ index.js:136
_onPacket @ socket.js:259
Emitter.emit @ index.js:136
onPacket @ transport.js:99
onData @ transport.js:91
ws.onmessage @ websocket.js:48
myOffersStore.ts:132 [myOffersStore] Interest accepted via API, waiting for socket event {message: 'Interest accepted and chat session created', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', interestId: 'cmcxmkz270003vlqkci4rxj4j', newStatus: 'ACCEPTED'}
chatNavigation.ts:27 🔄 Resolving chatSessionId to transactionId: cmcxml2740005vlqk1cln5zpz
transactionalChatApi.ts:111 🔄 API: Resolving chat session to transaction: cmcxml2740005vlqk1cln5zpz
transactionalChatApi.ts:119 ✅ API: Successfully resolved chat session to transaction
chatNavigation.ts:31 ✅ Resolved to transactionId: cmcxml27z0006vlqkbu4pl9vm
useClientLogger.ts:67 [INFO] Route navigation started {from: '/my-offers', to: '/transaction/cmcxml27z0006vlqkbu4pl9vm', routeName: 'TransactionalChat', requiresAuth: true, requiresPhoneVerified: false}
useClientLogger.ts:67 [INFO] User action: navigation {to: '/transaction/cmcxml27z0006vlqkbu4pl9vm', from: '/my-offers', timestamp: '2025-07-10T16:52:00.766Z'}
useClientLogger.ts:67 [INFO] Route navigation completed {from: '/my-offers', to: '/transaction/cmcxml27z0006vlqkbu4pl9vm', routeName: 'TransactionalChat'}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'paymentInfo', isUsersTurn: false, shouldShow: false}
transactionalChatStore.ts:806 🔄 RESETTING transactional chat store
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm 
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {}
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 1
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-paymentInfo', type: 'actionCard', timestamp: '2025-07-10T16:52:00.905Z', actionType: 'paymentInfo', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 1
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: null, newStatus: 'PENDING_AGREEMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}}
ActionCard.vue:68 🔍 ActionCard - Payment Methods Summary: {actionCardId: 'action-paymentInfo', filteredMethodsCount: 1, targetCurrency: 'CAD', actionType: 'paymentInfo', methods: Array(1), …}
SmartPaymentInfoSection.vue:105 🔄 SmartPaymentInfoSection - currentMethod watcher: {newMethod: Proxy(Object), oldMethod: undefined, selectedMethodId: null}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'paymentInfo', isUsersTurn: true, shouldShow: false}
SmartPaymentInfoSection.vue:359 � SmartPaymentInfoSection mounted
ActionCard.vue:41 🔍 ActionCard mounted - actionType: paymentInfo isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
auth.ts:260 [AuthStore] Token expiry check: 1389 minutes until expiration
ActionCard.vue:254 🎯 ActionCard - handlePaymentInfoConfirmed called: {method: Proxy(Object), validationStatus: 'complete'}
transactionalChatApi.ts:201 🔄 API: Performing action: paymentInfo for transaction: cmcxml27z0006vlqkbu4pl9vm
SmartPaymentInfoSection.vue:105 🔄 SmartPaymentInfoSection - currentMethod watcher: {newMethod: Proxy(Object), oldMethod: Proxy(Object), selectedMethodId: 'cmckmw5p10001vl6sbvvcqz1v'}
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'CONFIRMED_FROM_PROFILE', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxkt1ct0001vlp0wkhqn6vx interests to PENDING_RESPONSE
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to PENDING_AGREEMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'PENDING_AGREEMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxml7he000avlqkxjnj323s', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:52:02.979Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxml7he000avlqkxjnj323s",\n  "c…kbu4pl9vm",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxml7he000avlqkxjnj323s', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:52:02.979Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxml7he000avlqkxjnj323s",\n  "type": "…sProvided",\n  "data": {\n    "username": "h"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 1
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 1
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}}
SmartPaymentInfoSection.vue:363 🔄 SmartPaymentInfoSection unmounted
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:779 ⚠️ No socket update received within 800ms, performing manual refetch
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 0
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 1
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: PENDING_AGREEMENT ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: paymentInfo
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: paymentInfo should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 1
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to AWAITING_FIRST_PAYER_DESIGNATION
transactionalChatStore.ts:122 📡 Received transaction status update: {offerId: 'cmcxmkvts0001vlqk627e47mh', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', status: 'AWAITING_FIRST_PAYER_DESIGNATION', currencyA: 'CAD', …}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmlckb000cvlqk3sdmj1mw', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:52:09.564Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmlckb000cvlqk3sdmj1mw",\n  "c…nId": "cmcxml27z0006vlqkbu4pl9vm",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmlckb000cvlqk3sdmj1mw', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.readyToNegotiate', createdAt: '2025-07-10T16:52:09.564Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmlckb000cvlqk3sdmj1mw",\n  "type": "…Chat.systemLogs.readyToNegotiate",\n  "data": {}\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmlcky000evlqkilccksa4', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:52:09.586Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmlcky000evlqkilccksa4",\n  "c…bu4pl9vm",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmlcky000evlqkilccksa4', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'transactionalChat.systemLogs.paymentDetailsProvided', createdAt: '2025-07-10T16:52:09.586Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmlcky000evlqkilccksa4",\n  "type": "…Provided",\n  "data": {\n    "username": "h2"\n  }\n}'}
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 1
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 4
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_DESIGNATION ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: negotiation
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-negotiation', type: 'actionCard', timestamp: '2025-07-10T16:52:09.668Z', actionType: 'negotiation', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: negotiation should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 4
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'PENDING_AGREEMENT', newStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
payerNegotiation.ts:348 [PayerNegotiationStore] Initializing socket listeners with centralized socket manager
payerNegotiation.ts:289 [PayerNegotiationStore] Setting up socket listeners with centralized socket manager
payerNegotiation.ts:134 [PayerNegotiationStore] fetchNegotiation called with transactionId: cmcxml27z0006vlqkbu4pl9vm
payerNegotiation.ts:144 [PayerNegotiationStore] Fetching negotiation details from: /transactions/cmcxml27z0006vlqkbu4pl9vm/payer-negotiation
payerNegotiation.ts:348 [PayerNegotiationStore] Initializing socket listeners with centralized socket manager
payerNegotiation.ts:289 [PayerNegotiationStore] Setting up socket listeners with centralized socket manager
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'negotiation', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: negotiation isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
prepare.js:1 🍍 "payerNegotiation" store installed 🆕
auth.ts:260 [AuthStore] Token expiry check: 1389 minutes until expiration
payerNegotiation.ts:148 [PayerNegotiationStore] API Response Data: {data: {…}}
payerNegotiation.ts:154 [PayerNegotiationStore] Attempting to set currentNegotiation to: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:156 [PayerNegotiationStore] currentNegotiation after set: Proxy(Object) {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:228 [PayerNegotiationStore] acceptCurrentProposal: Attempting for txId: cmcxml27z0006vlqkbu4pl9vm
auth.ts:260 [AuthStore] Token expiry check: 1389 minutes until expiration
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmm48f000gvlqkwd2qb5nw', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:52:45.423Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmm48f000gvlqkwd2qb5nw",\n  "c…kbu4pl9vm",\n  "data": {\n    "username": "h"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmm48f000gvlqkwd2qb5nw', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:52:45.423Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmm48f000gvlqkwd2qb5nw",\n  "type": "…al.agreed",\n  "data": {\n    "username": "h"\n  }\n}'}
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxmkvts0001vlqk627e47mh interests to READY_TO_NEGOTIATE
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:233 [PayerNegotiationStore] acceptCurrentProposal: Full API Response object: {
  "data": {
    "data": {
      "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
      "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
      "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
      "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
      "partyA_receivingInfoStatus": "PROVIDED",
      "partyB_receivingInfoStatus": "PROVIDED",
      "partyA_PaymentReceivingInfo": {
        "id": "cmckmrod00008vlv4gmcjhuqp",
        "bankName": "bankiran",
        "accountNumber": "1111",
        "accountHolderName": "h2",
        "isDefaultForUser": true
      },
      "partyB_PaymentReceivingInfo": {
        "id": "cmcknhjkd0001vlq84d32bu7q",
        "bankName": "bankanada",
        "accountNumber": "1111",
        "accountHolderName": "H",
        "isDefaultForUser": true
      },
      "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
      "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
      "systemRecommendationRule": "REPUTATION",
      "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
      "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
      "currentProposal_ById": "system",
      "currentProposal_Message": null,
      "partyA_agreedToCurrentProposal": false,
      "partyB_agreedToCurrentProposal": true,
      "negotiationStatus": "READY_TO_NEGOTIATE",
      "finalizedPayerId": null,
      "paymentTimerDueDate": null,
      "createdAt": "2025-07-10T16:52:02.929Z",
      "updatedAt": "2025-07-10T16:52:45.395Z",
      "isFinalOffer": false
    }
  },
  "status": 200,
  "statusText": "OK",
  "headers": {
    "content-length": "1215",
    "content-type": "application/json"
  },
  "config": {
    "transitional": {
      "silentJSONParsing": true,
      "forcedJSONParsing": true,
      "clarifyTimeoutError": false
    },
    "adapter": [
      "xhr",
      "http",
      "fetch"
    ],
    "transformRequest": [
      null
    ],
    "transformResponse": [
      null
    ],
    "timeout": 0,
    "xsrfCookieName": "XSRF-TOKEN",
    "xsrfHeaderName": "X-XSRF-TOKEN",
    "maxContentLength": -1,
    "maxBodyLength": -1,
    "env": {},
    "headers": {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": "application/x-www-form-urlencoded",
      "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.j1eXx1zOzTHF2nUfaIWjGWdHDaTyQ8uC2fTH87zwBFk"
    },
    "baseURL": "http://localhost:3000/api",
    "method": "post",
    "url": "/transactions/cmcxml27z0006vlqkbu4pl9vm/payer-negotiation/agree",
    "allowAbsoluteUrls": true
  },
  "request": {}
}
payerNegotiation.ts:234 [PayerNegotiationStore] acceptCurrentProposal: API response.data: {
  "data": {
    "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
    "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
    "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
    "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
    "partyA_receivingInfoStatus": "PROVIDED",
    "partyB_receivingInfoStatus": "PROVIDED",
    "partyA_PaymentReceivingInfo": {
      "id": "cmckmrod00008vlv4gmcjhuqp",
      "bankName": "bankiran",
      "accountNumber": "1111",
      "accountHolderName": "h2",
      "isDefaultForUser": true
    },
    "partyB_PaymentReceivingInfo": {
      "id": "cmcknhjkd0001vlq84d32bu7q",
      "bankName": "bankanada",
      "accountNumber": "1111",
      "accountHolderName": "H",
      "isDefaultForUser": true
    },
    "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
    "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
    "systemRecommendationRule": "REPUTATION",
    "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
    "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
    "currentProposal_ById": "system",
    "currentProposal_Message": null,
    "partyA_agreedToCurrentProposal": false,
    "partyB_agreedToCurrentProposal": true,
    "negotiationStatus": "READY_TO_NEGOTIATE",
    "finalizedPayerId": null,
    "paymentTimerDueDate": null,
    "createdAt": "2025-07-10T16:52:02.929Z",
    "updatedAt": "2025-07-10T16:52:45.395Z",
    "isFinalOffer": false
  }
}
payerNegotiation.ts:235 [PayerNegotiationStore] acceptCurrentProposal: API response.data.data (if exists): {
  "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:52:02.929Z",
  "updatedAt": "2025-07-10T16:52:45.395Z",
  "isFinalOffer": false
}
payerNegotiation.ts:238 [PayerNegotiationStore] acceptCurrentProposal: Value to be assigned to currentNegotiation: {
  "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:52:02.929Z",
  "updatedAt": "2025-07-10T16:52:45.395Z",
  "isFinalOffer": false
}
payerNegotiation.ts:240 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation BEFORE set: {
  "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:52:02.929Z",
  "updatedAt": "2025-07-10T16:52:45.395Z",
  "isFinalOffer": false
}
payerNegotiation.ts:242 [PayerNegotiationStore] acceptCurrentProposal: currentNegotiation AFTER set: {
  "negotiationId": "cmcxml7g00008vlqk26xnsbfb",
  "transactionId": "cmcxml27z0006vlqkbu4pl9vm",
  "partyA_Id": "cmckmpoq60001vlpgo82crgdt",
  "partyB_Id": "cmckmpopk0000vlpgl7qffd1c",
  "partyA_receivingInfoStatus": "PROVIDED",
  "partyB_receivingInfoStatus": "PROVIDED",
  "partyA_PaymentReceivingInfo": {
    "id": "cmckmrod00008vlv4gmcjhuqp",
    "bankName": "bankiran",
    "accountNumber": "1111",
    "accountHolderName": "h2",
    "isDefaultForUser": true
  },
  "partyB_PaymentReceivingInfo": {
    "id": "cmcknhjkd0001vlq84d32bu7q",
    "bankName": "bankanada",
    "accountNumber": "1111",
    "accountHolderName": "H",
    "isDefaultForUser": true
  },
  "systemRecommendedPayerId": "cmckmpoq60001vlpgo82crgdt",
  "systemRecommendationReason": "h2 has a lower reputation level (3) and should pay first.",
  "systemRecommendationRule": "REPUTATION",
  "systemRecommendationDetails": "{\"reputationA\":3,\"reputationB\":5}",
  "currentProposal_PayerId": "cmckmpoq60001vlpgo82crgdt",
  "currentProposal_ById": "system",
  "currentProposal_Message": null,
  "partyA_agreedToCurrentProposal": false,
  "partyB_agreedToCurrentProposal": true,
  "negotiationStatus": "READY_TO_NEGOTIATE",
  "finalizedPayerId": null,
  "paymentTimerDueDate": null,
  "createdAt": "2025-07-10T16:52:02.929Z",
  "updatedAt": "2025-07-10T16:52:45.395Z",
  "isFinalOffer": false
}
payerNegotiation.ts:258 [PayerNegotiationStore] acceptCurrentProposal: Finished for txId: cmcxml27z0006vlqkbu4pl9vm. Loading: false
ActionCard.vue:275 Negotiation updated: Proxy(Object) {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmm57l000ivlqk8zeqphd8', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:52:46.690Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmm57l000ivlqk8zeqphd8",\n  "c…bu4pl9vm",\n  "data": {\n    "username": "h2"\n  }\n}', hasData: true, dataKeys: Array(1), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmm57l000ivlqk8zeqphd8', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.agreed', createdAt: '2025-07-10T16:52:46.690Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmm57l000ivlqk8zeqphd8",\n  "type": "…l.agreed",\n  "data": {\n    "username": "h2"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: 'b6479b63-61e5-450f-8b8a-e4c719bd5e0d', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h2 agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: 'b6479b63-61e5-450f-8b8a-e4c719bd5e0d', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Proposal Agreement: h2 agreed to the payer proposal in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"b6479b63-61e5-450f-8b8a-e4c719bd5e0d","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Proposal Agreement: h2 agreed to the payer proposal in your transaction.","isRead":false,"createdAt":"2025-07-10T16:52:46.697Z","updatedAt":"2025-07-10T16:52:46.697Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxml27z0006vlqkbu4pl9vm","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxml7g00008vlqk26xnsbfb\",\"agreedUserId\":\"cmckmpoq60001vlpgo82crgdt\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification b6479b63-61e5-450f-8b8a-e4c719bd5e0d.
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to AWAITING_FIRST_PAYER_PAYMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmm5at000kvlqkmv5cfnga', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:52:46.805Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmm5at000kvlqkmv5cfnga",\n  "c…"h2",\n    "dueDate": "10/07/2025, 19:52:46"\n  }\n}', hasData: true, dataKeys: Array(2), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmm5at000kvlqkmv5cfnga', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.proposal.bothAgreed', createdAt: '2025-07-10T16:52:46.805Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmm5at000kvlqkmv5cfnga",\n  "type": "…"h2",\n    "dueDate": "10/07/2025, 19:52:46"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '62a6f52b-3113-4d3f-ad76-41ca995b4332', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Both parties agreed. h2 will make the first paymen…ransaction cmcxml27, due by 10/07/2025, 19:52:46.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '62a6f52b-3113-4d3f-ad76-41ca995b4332', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Both parties agreed. h2 will make the first paymen…ransaction cmcxml27, due by 10/07/2025, 19:52:46.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"62a6f52b-3113-4d3f-ad76-41ca995b4332","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Both parties agreed. h2 will make the first payment for transaction cmcxml27, due by 10/07/2025, 19:52:46.","isRead":false,"createdAt":"2025-07-10T16:52:46.825Z","updatedAt":"2025-07-10T16:52:46.825Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxml27z0006vlqkbu4pl9vm","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification 62a6f52b-3113-4d3f-ad76-41ca995b4332.
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: '3d2e316a-710e-4a39-85f3-7831df3703e1', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: '3d2e316a-710e-4a39-85f3-7831df3703e1', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'Payer Decision Finalized: The payer decision has b…finalized. h2 will pay first in your transaction.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"3d2e316a-710e-4a39-85f3-7831df3703e1","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"Payer Decision Finalized: The payer decision has been finalized. h2 will pay first in your transaction.","isRead":false,"createdAt":"2025-07-10T16:52:46.848Z","updatedAt":"2025-07-10T16:52:46.848Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxml27z0006vlqkbu4pl9vm","actorId":null,"actorUsername":null,"data":"{\"negotiationId\":\"cmcxml7g00008vlqk26xnsbfb\",\"finalizedPayerId\":\"cmckmpoq60001vlpgo82crgdt\"}"}
notificationStore.ts:159 [NotificationStore] Added new notification 3d2e316a-710e-4a39-85f3-7831df3703e1.
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
centralizedSocketManager.ts:596 🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:255 🔥 [myOffersStore] Handling NEGOTIATION_STATE_UPDATED event: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
myOffersStore.ts:289 [myOffersStore] Updated negotiation status for offer cmcxmkvts0001vlqk627e47mh interests to FINALIZED
payerNegotiation.ts:303 [PayerNegotiationStore] Received NEGOTIATION_STATE_UPDATED: {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
payerNegotiation.ts:323 [PayerNegotiationStore] Negotiation state updated via socket: Proxy(Object) {negotiationId: 'cmcxml7g00008vlqk26xnsbfb', transactionId: 'cmcxml27z0006vlqkbu4pl9vm', partyA_Id: 'cmckmpoq60001vlpgo82crgdt', partyB_Id: 'cmckmpopk0000vlpgl7qffd1c', partyA_receivingInfoStatus: 'PROVIDED', …}
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 2
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 7
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_PAYMENT ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxml27z0006vlqkbu4pl9vm
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_DESIGNATION', newStatus: 'AWAITING_FIRST_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makePayment', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_makePayment isUsersTurn: false
auth.ts:260 [AuthStore] Token expiry check: 1389 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makePayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makePayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 7
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to AWAITING_SECOND_PAYER_CONFIRMATION
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmmbro000mvlqkbofl9l44', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:52:55.188Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmmbro000mvlqkbofl9l44",\n  "c…"otherUser": "h",\n    "amount": "5,000 CAD"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmmbro000mvlqkbofl9l44', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:52:55.188Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmmbro000mvlqkbofl9l44",\n  "type": "…"otherUser": "h",\n    "amount": "5,000 CAD"\n  }\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 3
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 8
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_CONFIRMATION ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
transactionalChatStore.ts:340 🔍 [fetchTransaction] Setting timer for transaction: cmcxml27z0006vlqkbu4pl9vm seconds: 7191
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-confirmReceipt', type: 'actionCard', timestamp: '2025-07-10T16:52:55.365Z', actionType: 'confirmReceipt', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 8
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_PAYMENT', newStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmReceipt', isUsersTurn: true, shouldShow: true}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: confirmReceipt isUsersTurn: true
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
auth.ts:260 [AuthStore] Token expiry check: 1389 minutes until expiration
transactionalChatApi.ts:201 🔄 API: Performing action: confirmReceipt for transaction: cmcxml27z0006vlqkbu4pl9vm
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to AWAITING_SECOND_PAYER_PAYMENT
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_SECOND_PAYER_PAYMENT', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmmgje000ovlqk92tkajrr', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:53:01.370Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmmgje000ovlqk92tkajrr",\n  "c…CAD",\n    "dueDate": "10/07/2025, 19:53:01"\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmmgje000ovlqk92tkajrr', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.confirmedFirst', createdAt: '2025-07-10T16:53:01.370Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmmgje000ovlqk92tkajrr",\n  "type": "…CAD",\n    "dueDate": "10/07/2025, 19:53:01"\n  }\n}'}
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 4
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 9
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_SECOND_PAYER_PAYMENT ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxml27z0006vlqkbu4pl9vm
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_CONFIRMATION', newStatus: 'AWAITING_SECOND_PAYER_PAYMENT', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'makeSecondPayment', isUsersTurn: true, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: makeSecondPayment isUsersTurn: true
auth.ts:260 [AuthStore] Token expiry check: 1388 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: makeSecondPayment
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: true
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: Proxy(Object) {id: 'action-makeSecondPayment', type: 'actionCard', timestamp: '2025-07-10T16:53:01.553Z', actionType: 'makeSecondPayment', data: {…}}
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: makeSecondPayment should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 9
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatApi.ts:201 🔄 API: Performing action: declareSecondPayerPayment for transaction: cmcxml27z0006vlqkbu4pl9vm
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to AWAITING_FIRST_PAYER_CONFIRMATION
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'AWAITING_FIRST_PAYER_CONFIRMATION', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmmku2000qvlqkapug4kfy', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:53:06.939Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmmku2000qvlqkapug4kfy",\n  "c…therUser": "h2",\n    "amount": "285.0M IRR"\n  }\n}', hasData: true, dataKeys: Array(3), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmmku2000qvlqkapug4kfy', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.declared', createdAt: '2025-07-10T16:53:06.939Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmmku2000qvlqkapug4kfy",\n  "type": "…therUser": "h2",\n    "amount": "285.0M IRR"\n  }\n}'}
centralizedSocketManager.ts:605 🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION: {id: 'dc8ae3bc-5bfd-4a7d-958b-a74cdbb4e06d', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'You have declared payment for transaction cmcxml27. Waiting for h2 to confirm.', isRead: false, …}
notificationStore.ts:220 [NotificationStore] Received NEW_NOTIFICATION via centralized manager: {id: 'dc8ae3bc-5bfd-4a7d-958b-a74cdbb4e06d', userId: 'cmckmpopk0000vlpgl7qffd1c', type: 'TRANSACTION_UPDATE', message: 'You have declared payment for transaction cmcxml27. Waiting for h2 to confirm.', isRead: false, …}
notificationStore.ts:152 [NotificationStore] addOrUpdateNotification called with: {"id":"dc8ae3bc-5bfd-4a7d-958b-a74cdbb4e06d","userId":"cmckmpopk0000vlpgl7qffd1c","type":"TRANSACTION_UPDATE","message":"You have declared payment for transaction cmcxml27. Waiting for h2 to confirm.","isRead":false,"createdAt":"2025-07-10T16:53:06.959Z","updatedAt":"2025-07-10T16:53:06.959Z","relatedEntityType":"TRANSACTION","relatedEntityId":"cmcxml27z0006vlqkbu4pl9vm","actorId":null,"actorUsername":null,"data":null}
notificationStore.ts:159 [NotificationStore] Added new notification dc8ae3bc-5bfd-4a7d-958b-a74cdbb4e06d.
transactionalChatApi.ts:212 ✅ API: Successfully performed action
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm 
transactionalChatStore.ts:304 ⚠️ Already loading transaction, skipping...
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 5
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 10
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: AWAITING_FIRST_PAYER_CONFIRMATION ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:348 🔍 [fetchTransaction] Step requires timer, fetching timer data separately...
transactionalChatApi.ts:233 🔄 API: Fetching timer status for transaction: cmcxml27z0006vlqkbu4pl9vm
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_SECOND_PAYER_PAYMENT', newStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', isCompleted: false, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
SmartPaymentDeclaredSection.vue:133 [DECLARED] Formatting amount {amount: 285000000, currency: 'IRR'}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'confirmFirstPaymentReceipt', isUsersTurn: false, shouldShow: false}
ActionCard.vue:41 🔍 ActionCard mounted - actionType: waiting_confirmFirstPaymentReceipt isUsersTurn: false
auth.ts:260 [AuthStore] Token expiry check: 1388 minutes until expiration
transactionalChatApi.ts:241 ✅ API: Successfully fetched timer status
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: confirmFirstPaymentReceipt
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: confirmFirstPaymentReceipt should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 10
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}
centralizedSocketManager.ts:590 🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:214 🔥 [myOffersStore] Handling TRANSACTION_STATUS_UPDATED event: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'COMPLETED', currencyA: 'CAD', …}
myOffersStore.ts:231 [myOffersStore] Updating transaction status for interest cmcxmkz270003vlqkci4rxj4j to COMPLETED
transactionalChatStore.ts:122 📡 Received transaction status update: {transactionId: 'cmcxml27z0006vlqkbu4pl9vm', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', offerId: 'cmcxmkvts0001vlqk627e47mh', status: 'COMPLETED', currencyA: 'CAD', …}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmmoa6000svlqkzumd9kmv', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:53:11.406Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmmoa6000svlqkzumd9kmv",\n  "c…  "amount": "285.0M IRR",\n    "dueDate": ""\n  }\n}', hasData: true, dataKeys: Array(5), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmmoa6000svlqkzumd9kmv', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.payment.confirmedSecond', createdAt: '2025-07-10T16:53:11.406Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmmoa6000svlqkzumd9kmv",\n  "type": "…  "amount": "285.0M IRR",\n    "dueDate": ""\n  }\n}'}
centralizedSocketManager.ts:584 🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE: {messageId: 'cmcxmmoaj000uvlqkowva4q4d', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:53:11.419Z', isSystemMessage: true, …}
transactionalChatStore.ts:163 🔍 [TransactionalChatStore] Raw system message received: {payload: '{\n  "messageId": "cmcxmmoaj000uvlqkowva4q4d",\n  "c…nId": "cmcxml27z0006vlqkbu4pl9vm",\n  "data": {}\n}', hasData: true, dataKeys: Array(0), currentChatSessionId: 'cmcxml2740005vlqk1cln5zpz'}
transactionalChatStore.ts:174 📡 Received system message for current session: {messageId: 'cmcxmmoaj000uvlqkowva4q4d', chatSessionId: 'cmcxml2740005vlqk1cln5zpz', content: 'systemMessages.transaction.complete', createdAt: '2025-07-10T16:53:11.419Z', isSystemMessage: true, …}
transactionalChatStore.ts:187 🔍 [TransactionalChatStore] Created system log feed item: {systemLog: '{\n  "id": "cmcxmmoaj000uvlqkowva4q4d",\n  "type": "…temMessages.transaction.complete",\n  "data": {}\n}'}
transactionalChatStore.ts:300 🔄 Fetching transaction: cmcxml27z0006vlqkbu4pl9vm (forced)
transactionalChatApi.ts:139 🔄 API: Fetching transaction chat details for: cmcxml27z0006vlqkbu4pl9vm
transactionalChatApi.ts:147 ✅ API: Successfully fetched transaction chat details
transactionalChatStore.ts:315 🔍 [fetchTransaction] API Response - currentStepIndex: 6
transactionalChatStore.ts:316 🔍 [fetchTransaction] API Response - feedItems count: 11
transactionalChatStore.ts:323 🔍 [fetchTransaction] otherUser after assignment: Proxy(Object) {id: 'cmckmpoq60001vlpgo82crgdt', name: 'h2', profilePic: undefined, reputation: 3}
transactionalChatStore.ts:325 🔍 [fetchTransaction] transactionDetails.value after assignment: {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:326 🔍 [fetchTransaction] transactionDetails after assignment: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
transactionalChatStore.ts:330 🔍 [fetchTransaction] Transaction status: COMPLETED ID: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:333 🔍 [fetchTransaction] feedItems after API response: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}}
transactionalChatStore.ts:344 🔍 [fetchTransaction] No active timer for transaction: cmcxml27z0006vlqkbu4pl9vm
transactionalChatStore.ts:369 🔍 [fetchTransaction] Current Step Key: finalized
transactionalChatStore.ts:370 🔍 [fetchTransaction] Is User's Turn: false
transactionalChatStore.ts:377 🔍 [fetchTransaction] Existing actionCard for current step: undefined
transactionalChatStore.ts:381 🔍 [fetchTransaction] Action card for current step: finalized should be handled by backend
transactionalChatStore.ts:104 🧹 [TransactionalChatStore] Clearing socket listeners
transactionalChatStore.ts:402 ✅ Transaction fetched successfully, final feed items count: 11
TransactionView.vue:138 🔍 [Confetti] Transaction status changed: {oldStatus: 'AWAITING_FIRST_PAYER_CONFIRMATION', newStatus: 'COMPLETED', isCompleted: true, completedEnum: 'COMPLETED', transactionId: 'cmcxml27z0006vlqkbu4pl9vm'}
TransactionView.vue:155 🎉 Transaction completed! Triggering confetti for transaction: cmcxml27z0006vlqkbu4pl9vm
TransactionView.vue:119 🎉 Confetti triggered by transaction completion!
TheHeaderBar.vue:49 [header] transactionDetails: Proxy(Object) {amountToSend: 285000000, amountToReceive: 5000, currencyFrom: 'IRR', currencyTo: 'CAD', isUserFirstPayer: false, …}
TheUnifiedFeed.vue:17 🔍 TheUnifiedFeed - feedItems: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}}
TheDynamicActionBar.vue:30 Dynamic Action Bar Debug: {stepKey: 'finalized', isUsersTurn: false, shouldShow: false}
useClientLogger.ts:67 [INFO] Autofill node removed by Vue.js {nodeName: 'DIV', nodeType: 1}

