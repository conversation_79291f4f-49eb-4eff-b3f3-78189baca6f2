#!/usr/bin/env node

/**
 * Simple test to verify the data field exists and works
 */

const { PrismaClient } = require('./backend/node_modules/@prisma/client');

async function testDataField() {
  console.log('🔍 Testing Data Field in ChatMessage');
  console.log('='.repeat(40));

  const prisma = new PrismaClient();

  try {
    // Test: Check existing system messages and their data field
    console.log('\n📊 Checking existing system messages...');
    
    const existingMessages = await prisma.chatMessage.findMany({
      where: {
        isSystemMessage: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10,
      select: {
        id: true,
        content: true,
        data: true,
        createdAt: true
      }
    });

    console.log(`Found ${existingMessages.length} existing system messages:`);
    existingMessages.forEach((msg, index) => {
      console.log(`   ${index + 1}. ${msg.content}`);
      console.log(`      ID: ${msg.id}`);
      console.log(`      Data: ${msg.data ? JSON.stringify(msg.data) : 'null'}`);
      console.log(`      Created: ${msg.createdAt.toISOString()}`);
      console.log('');
    });

    if (existingMessages.length > 0) {
      console.log('✅ Data field is accessible and working!');
      
      // Check if any messages have data
      const messagesWithData = existingMessages.filter(msg => msg.data !== null);
      console.log(`📊 Messages with data: ${messagesWithData.length}/${existingMessages.length}`);
      
      if (messagesWithData.length === 0) {
        console.log('⚠️  No messages have data yet (expected for legacy messages)');
        console.log('   New messages should start storing data after backend restart');
      }
    } else {
      console.log('⚠️  No system messages found in database');
    }

    console.log('\n🎉 Data field test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    
    if (error.message.includes('Unknown column') || error.message.includes('data')) {
      console.error('\n🚨 CRITICAL: Data field does not exist in database!');
      console.error('   Please run the migration again.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDataField().catch(console.error);
