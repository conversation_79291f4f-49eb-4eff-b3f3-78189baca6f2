#!/usr/bin/env node

/**
 * Test script to verify system message fixes after database migration
 */

const { PrismaClient } = require('./backend/node_modules/@prisma/client');

async function testSystemMessageFix() {
  console.log('🔍 Testing System Message Fix After Database Migration');
  console.log('='.repeat(60));

  const prisma = new PrismaClient();

  try {
    // Test 1: Verify data field exists in schema
    console.log('\n📊 Test 1: Verifying database schema...');
    
    // Try to create a test message with data field
    const testMessage = await prisma.chatMessage.create({
      data: {
        chatSessionId: 'test-session-' + Date.now(),
        content: 'systemMessages.test',
        isSystemMessage: true,
        senderId: null,
        transactionId: 'test-transaction-' + Date.now(),
        data: {
          username: 'TestUser',
          amount: '100 USD',
          testField: 'This is a test'
        }
      }
    });

    console.log('✅ Database schema test passed - data field exists and accepts JSON');
    console.log(`   Created test message: ${testMessage.id}`);
    console.log(`   Stored data: ${JSON.stringify(testMessage.data, null, 2)}`);

    // Test 2: Verify data retrieval
    console.log('\n📊 Test 2: Verifying data retrieval...');
    
    const retrievedMessage = await prisma.chatMessage.findUnique({
      where: { id: testMessage.id }
    });

    if (retrievedMessage && retrievedMessage.data) {
      console.log('✅ Data retrieval test passed');
      console.log(`   Retrieved data: ${JSON.stringify(retrievedMessage.data, null, 2)}`);
    } else {
      console.log('❌ Data retrieval test failed - no data found');
    }

    // Test 3: Check existing system messages
    console.log('\n📊 Test 3: Checking existing system messages...');
    
    const existingMessages = await prisma.chatMessage.findMany({
      where: {
        isSystemMessage: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });

    console.log(`Found ${existingMessages.length} existing system messages:`);
    existingMessages.forEach((msg, index) => {
      console.log(`   ${index + 1}. ${msg.content} (${msg.createdAt.toISOString()})`);
      console.log(`      Data: ${msg.data ? JSON.stringify(msg.data) : 'null'}`);
    });

    // Clean up test message
    await prisma.chatMessage.delete({
      where: { id: testMessage.id }
    });
    console.log('\n🧹 Cleaned up test message');

    console.log('\n🎉 All tests passed! System message fix is working correctly.');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database migration applied successfully');
    console.log('   ✅ Data field accepts and stores JSON data');
    console.log('   ✅ Data field can be retrieved properly');
    console.log('   ✅ Existing system messages are accessible');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    
    if (error.message.includes('Unknown argument `data`')) {
      console.error('\n🚨 CRITICAL: Database migration was not applied!');
      console.error('   The `data` field does not exist in the ChatMessage table.');
      console.error('   Please run: cd backend && npx prisma migrate dev --name add-chat-message-data-field');
    } else if (error.code === 'P2002') {
      console.error('\n⚠️  Constraint violation - this is expected for test data');
    } else {
      console.error('\n🔧 Unexpected error occurred. Please check the database connection and schema.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSystemMessageFix().catch(console.error);
