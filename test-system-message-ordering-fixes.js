#!/usr/bin/env node

/**
 * Test script to validate system message ordering and duplication fixes
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 TESTING SYSTEM MESSAGE ORDERING & DUPLICATION FIXES');
console.log('='.repeat(60));

let allTestsPassed = true;
const issues = [];

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}`);
  if (details) console.log(`   ${details}`);
  if (!passed) {
    allTestsPassed = false;
    issues.push(`${testName}: ${details}`);
  }
}

// Test 1: Verify Extra Message Fix
console.log('\n🔧 Testing Extra Message Fix...');

function testExtraMessageFix() {
  const chatServiceFile = 'backend/src/services/transactionalChatService.ts';
  if (fs.existsSync(chatServiceFile)) {
    const content = fs.readFileSync(chatServiceFile, 'utf8');
    
    // Check that the problematic addSystemMessage call is removed/commented
    const hasRemovedExtraMessage = !content.includes('addSystemMessage(\n          transaction.chatSessionId,\n          \'transactionalChat.systemLogs.firstPaymentConfirmed\',\n          transactionId\n        );');
    
    const hasCommentExplaining = content.includes('// NOTE: Removed addSystemMessage for firstPaymentConfirmed here') ||
                                content.includes('// This was causing an extra message after transaction completion');
    
    logTest(
      'Extra message call removed from confirmFirstPayerPayment',
      hasRemovedExtraMessage,
      hasRemovedExtraMessage ? 'Problematic addSystemMessage call removed' : 'Extra message call still present'
    );
    
    logTest(
      'Fix includes explanatory comment',
      hasCommentExplaining,
      hasCommentExplaining ? 'Comment explains why the call was removed' : 'Missing explanatory comment'
    );
  } else {
    logTest('TransactionalChatService file exists', false, 'File not found');
  }
}

// Test 2: Verify Message Ordering Fix
console.log('\n🔧 Testing Message Ordering Fix...');

function testMessageOrderingFix() {
  const chatServiceFile = 'backend/src/services/transactionalChatService.ts';
  if (fs.existsSync(chatServiceFile)) {
    const content = fs.readFileSync(chatServiceFile, 'utf8');
    
    // Check for improved logic that gets updated status from database
    const hasUpdatedLogic = content.includes('const updatedNegotiation = await this.prisma.payerNegotiation.update(');
    const hasProperStatusCheck = content.includes('const currentPartyAStatus = updatedNegotiation.partyA_receivingInfoStatus;');
    const hasLogging = content.includes('Both parties have provided payment info. Moving to negotiation phase.');
    
    logTest(
      'Updated negotiation logic implemented',
      hasUpdatedLogic,
      hasUpdatedLogic ? 'Database update returns current status' : 'Missing updated logic'
    );
    
    logTest(
      'Proper status checking from database',
      hasProperStatusCheck,
      hasProperStatusCheck ? 'Status checked from updated database values' : 'Missing proper status check'
    );
    
    logTest(
      'Added logging for debugging',
      hasLogging,
      hasLogging ? 'Logging added to track when both parties have provided info' : 'Missing debug logging'
    );
  }
}

// Test 3: Verify Translation Files Still Intact
console.log('\n🌐 Testing Translation Files...');

function testTranslationFiles() {
  const translationFiles = [
    'frontend/src/locales/en/systemMessages.json',
    'frontend/src/locales/fa/systemMessages.json'
  ];
  
  translationFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      const translations = JSON.parse(content);
      
      const hasTransactionComplete = translations.transaction?.complete;
      const hasPaymentConfirmed = translations.payment?.confirmedFirst;
      const hasReadyToNegotiate = translations.readyToNegotiate || 
                                 (translations.transactionalChat && translations.transactionalChat.systemLogs && translations.transactionalChat.systemLogs.readyToNegotiate);
      
      const lang = file.includes('/en/') ? 'English' : 'Persian';
      
      logTest(
        `${lang} transaction complete message exists`,
        hasTransactionComplete,
        hasTransactionComplete ? 'Transaction completion message available' : 'Missing transaction completion message'
      );
      
      logTest(
        `${lang} payment confirmed message exists`,
        hasPaymentConfirmed,
        hasPaymentConfirmed ? 'Payment confirmation message available' : 'Missing payment confirmation message'
      );
      
      logTest(
        `${lang} ready to negotiate message exists`,
        hasReadyToNegotiate,
        hasReadyToNegotiate ? 'Ready to negotiate message available' : 'Missing ready to negotiate message'
      );
    }
  });
}

// Test 4: Check Database Schema
console.log('\n📊 Testing Database Schema...');

function testDatabaseSchema() {
  const schemaFile = 'backend/prisma/schema.prisma';
  if (fs.existsSync(schemaFile)) {
    const content = fs.readFileSync(schemaFile, 'utf8');
    
    const hasDataField = content.includes('data            Json?');
    const hasChatMessageModel = content.includes('model ChatMessage');
    
    logTest(
      'ChatMessage model has data field',
      hasDataField,
      hasDataField ? 'Data field available for contextual information' : 'Missing data field'
    );
    
    logTest(
      'ChatMessage model exists',
      hasChatMessageModel,
      hasChatMessageModel ? 'ChatMessage model found in schema' : 'Missing ChatMessage model'
    );
  }
}

// Test 5: Verify Backend Service Integration
console.log('\n🔗 Testing Backend Service Integration...');

function testBackendIntegration() {
  const transactionServiceFile = 'backend/src/services/transactionService.ts';
  if (fs.existsSync(transactionServiceFile)) {
    const content = fs.readFileSync(transactionServiceFile, 'utf8');
    
    const hasCreateAndEmitSystemMessage = content.includes('createAndEmitSystemMessage');
    const hasDataParameter = content.includes('messageData');
    const hasConfirmReceiptMethod = content.includes('async confirmReceipt(');
    
    logTest(
      'TransactionService has system message creation',
      hasCreateAndEmitSystemMessage,
      hasCreateAndEmitSystemMessage ? 'System message creation method available' : 'Missing system message creation'
    );
    
    logTest(
      'System messages include contextual data',
      hasDataParameter,
      hasDataParameter ? 'Contextual data parameter used' : 'Missing contextual data'
    );
    
    logTest(
      'Confirm receipt method exists',
      hasConfirmReceiptMethod,
      hasConfirmReceiptMethod ? 'Payment confirmation method available' : 'Missing confirmation method'
    );
  }
}

// Run all tests
testExtraMessageFix();
testMessageOrderingFix();
testTranslationFiles();
testDatabaseSchema();
testBackendIntegration();

// Summary
console.log('\n' + '='.repeat(60));
console.log('📋 VALIDATION SUMMARY');
console.log('='.repeat(60));

if (allTestsPassed) {
  console.log('🎉 ALL TESTS PASSED! System message ordering and duplication fixes are complete.');
  console.log('\n✅ Key fixes implemented:');
  console.log('   • Removed extra firstPaymentConfirmed message after transaction completion');
  console.log('   • Fixed readyToNegotiate message timing to wait for both users');
  console.log('   • Enhanced database status checking for accurate party status');
  console.log('   • Maintained all translation files and database schema');
  console.log('   • Preserved backend service integration');
  console.log('\n🚀 Ready for testing with real transaction flow!');
} else {
  console.log('❌ SOME TESTS FAILED. Please address the following issues:');
  issues.forEach(issue => console.log(`   • ${issue}`));
  console.log('\n🔧 Please fix these issues before proceeding.');
}

console.log('\n📝 Next steps:');
console.log('   1. Restart backend service to load the fixes');
console.log('   2. Test complete transaction flow');
console.log('   3. Verify no extra messages appear after completion');
console.log('   4. Confirm proper chronological ordering');
console.log('   5. Test page refresh message restoration');

process.exit(allTestsPassed ? 0 : 1);
