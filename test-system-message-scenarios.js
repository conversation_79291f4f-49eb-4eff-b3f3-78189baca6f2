#!/usr/bin/env node

/**
 * Test Script for System Message Scenarios
 * 
 * This script tests the specific failing scenarios identified in the screenshot
 * to ensure they now display complete contextual information.
 */

const fs = require('fs');

console.log('🧪 Testing System Message Scenarios...\n');

// Test 1: Translation File Validation
console.log('📋 Test 1: Translation File Validation');

function testTranslationFiles() {
  const files = [
    { path: 'frontend/src/locales/en/systemMessages.json', lang: 'EN' },
    { path: 'frontend/src/locales/fa/systemMessages.json', lang: 'FA' }
  ];

  const testCases = [
    {
      key: 'payment.declared',
      expectedPlaceholders: ['{username}', '{amount}', '{otherUser}'],
      description: 'Payment declaration message'
    },
    {
      key: 'proposal.agreed',
      expectedPlaceholders: ['{username}'],
      description: 'Proposal agreement message'
    },
    {
      key: 'proposal.bothAgreed',
      expectedPlaceholders: ['{firstPayer}', '{dueDate}'],
      description: 'Both parties agreed message'
    },
    {
      key: 'transactionalChat.systemLogs.paymentDetailsProvided',
      expectedPlaceholders: ['{username}'],
      description: 'Payment details provided message'
    }
  ];

  files.forEach(({ path, lang }) => {
    try {
      const content = fs.readFileSync(path, 'utf8');
      const translations = JSON.parse(content);
      
      console.log(`  📁 Testing ${lang} translations...`);
      
      testCases.forEach(({ key, expectedPlaceholders, description }) => {
        const keyParts = key.split('.');
        let current = translations;
        
        // Navigate to the nested key
        for (const part of keyParts) {
          if (!current || !current[part]) {
            console.log(`    ❌ Missing key: ${key}`);
            return;
          }
          current = current[part];
        }
        
        const message = current;
        const missingPlaceholders = expectedPlaceholders.filter(placeholder => 
          !message.includes(placeholder)
        );
        
        if (missingPlaceholders.length === 0) {
          console.log(`    ✅ ${description} - All placeholders present`);
        } else {
          console.log(`    ❌ ${description} - Missing: ${missingPlaceholders.join(', ')}`);
        }
      });
      
    } catch (error) {
      console.log(`    ❌ Error reading ${lang} file: ${error.message}`);
    }
  });
}

testTranslationFiles();

// Test 2: Backend Service Validation
console.log('\n🔧 Test 2: Backend Service Validation');

function testBackendServices() {
  const services = [
    'backend/src/services/transactionService.ts',
    'backend/src/services/transactionalChatService.ts',
    'backend/src/services/payerNegotiationService.ts'
  ];

  services.forEach(servicePath => {
    try {
      const content = fs.readFileSync(servicePath, 'utf8');
      const serviceName = servicePath.split('/').pop();
      
      console.log(`  📁 Testing ${serviceName}...`);
      
      // Check for debug logging
      const hasDebugLogging = content.includes('console.log') && 
                             content.includes('[TransactionalChatService]') ||
                             content.includes('[TransactionService]') ||
                             content.includes('[PayerNegotiationService]');
      
      if (hasDebugLogging) {
        console.log(`    ✅ Debug logging implemented`);
      } else {
        console.log(`    ❌ Debug logging missing`);
      }
      
      // Check for data parameter usage
      const hasDataParameter = content.includes('messageData') || 
                              content.includes('params') ||
                              content.includes('data:');
      
      if (hasDataParameter) {
        console.log(`    ✅ Data parameter usage found`);
      } else {
        console.log(`    ❌ Data parameter usage missing`);
      }
      
      // Check for username extraction
      const hasUsernameExtraction = content.includes('username') && 
                                   content.includes('findUnique');
      
      if (hasUsernameExtraction) {
        console.log(`    ✅ Username extraction logic found`);
      } else {
        console.log(`    ❌ Username extraction logic missing`);
      }
      
    } catch (error) {
      console.log(`    ❌ Error reading ${servicePath}: ${error.message}`);
    }
  });
}

testBackendServices();

// Test 3: Frontend Component Validation
console.log('\n🎨 Test 3: Frontend Component Validation');

function testFrontendComponents() {
  const components = [
    { path: 'frontend/src/components/TransactionalChat/SystemLog.vue', name: 'SystemLog' },
    { path: 'frontend/src/stores/transactionalChat/transactionalChatStore.ts', name: 'Store' }
  ];

  components.forEach(({ path, name }) => {
    try {
      const content = fs.readFileSync(path, 'utf8');
      
      console.log(`  📁 Testing ${name}...`);
      
      // Check for data field usage
      const hasDataField = content.includes('props.item.data') || 
                          content.includes('payload.data');
      
      if (hasDataField) {
        console.log(`    ✅ Data field usage found`);
      } else {
        console.log(`    ❌ Data field usage missing`);
      }
      
      // Check for debug logging
      const hasDebugLogging = content.includes('console.log') && 
                             (content.includes('[SystemLog]') || 
                              content.includes('[TransactionalChatStore]'));
      
      if (hasDebugLogging) {
        console.log(`    ✅ Debug logging implemented`);
      } else {
        console.log(`    ❌ Debug logging missing`);
      }
      
      // Check for translation processing
      const hasTranslationProcessing = content.includes('t(') && 
                                      content.includes('processedData');
      
      if (hasTranslationProcessing) {
        console.log(`    ✅ Translation processing found`);
      } else {
        console.log(`    ❌ Translation processing missing`);
      }
      
    } catch (error) {
      console.log(`    ❌ Error reading ${path}: ${error.message}`);
    }
  });
}

testFrontendComponents();

// Test 4: Specific Scenario Validation
console.log('\n🎯 Test 4: Specific Scenario Validation');

function testSpecificScenarios() {
  console.log('  📋 Testing specific failing scenarios from screenshot...');

  const scenarios = [
    {
      description: 'Payment details submission',
      expectedKey: 'transactionalChat.systemLogs.paymentDetailsProvided',
      expectedData: ['username'],
      expectedResult: 'جزئیات پرداخت توسط Alice ارسال شد'
    },
    {
      description: 'Counter-proposal acceptance',
      expectedKey: 'systemMessages.proposal.agreed',
      expectedData: ['username'],
      expectedResult: 'پیشنهاد توسط Alice پذیرفته شد'
    },
    {
      description: 'Payment declaration',
      expectedKey: 'systemMessages.payment.declared',
      expectedData: ['username', 'amount', 'otherUser'],
      expectedResult: 'اعلام پرداخت توسط Alice برای مبلغ 28.5M IRR ثبت شد'
    }
  ];

  scenarios.forEach(({ description, expectedKey, expectedData, expectedResult }) => {
    console.log(`    🔍 ${description}:`);
    console.log(`      - Expected key: ${expectedKey}`);
    console.log(`      - Expected data: ${expectedData.join(', ')}`);
    console.log(`      - Expected result: ${expectedResult}`);
    console.log(`      ✅ Scenario documented for testing`);
  });
}

// Test 5: Translation File Validation (Updated)
console.log('\n🔧 Test 5: Updated Translation File Validation');

function testUpdatedTranslations() {
  console.log('  📋 Validating fixed translation files...');

  try {
    // Test Persian translations
    const faContent = fs.readFileSync('frontend/src/locales/fa/transactionalChat.json', 'utf8');
    const faTranslations = JSON.parse(faContent);

    const paymentDetailsFA = faTranslations.systemLogs.paymentDetailsProvided;
    if (paymentDetailsFA.includes('{username}')) {
      console.log(`    ✅ Persian paymentDetailsProvided includes {username}: "${paymentDetailsFA}"`);
    } else {
      console.log(`    ❌ Persian paymentDetailsProvided missing {username}: "${paymentDetailsFA}"`);
    }

    // Test English translations
    const enContent = fs.readFileSync('frontend/src/locales/en/transactionalChat.json', 'utf8');
    const enTranslations = JSON.parse(enContent);

    const paymentDetailsEN = enTranslations.systemLogs.paymentDetailsProvided;
    if (paymentDetailsEN.includes('{username}')) {
      console.log(`    ✅ English paymentDetailsProvided includes {username}: "${paymentDetailsEN}"`);
    } else {
      console.log(`    ❌ English paymentDetailsProvided missing {username}: "${paymentDetailsEN}"`);
    }

    // Test systemMessages.json
    const systemFA = fs.readFileSync('frontend/src/locales/fa/systemMessages.json', 'utf8');
    const systemTranslations = JSON.parse(systemFA);

    const proposalAgreed = systemTranslations.proposal.agreed;
    if (proposalAgreed.includes('{username}')) {
      console.log(`    ✅ Persian proposal.agreed includes {username}: "${proposalAgreed}"`);
    } else {
      console.log(`    ❌ Persian proposal.agreed missing {username}: "${proposalAgreed}"`);
    }

    const paymentDeclared = systemTranslations.payment.declared;
    if (paymentDeclared.includes('{username}') && paymentDeclared.includes('{amount}') && paymentDeclared.includes('{otherUser}')) {
      console.log(`    ✅ Persian payment.declared includes all placeholders: "${paymentDeclared}"`);
    } else {
      console.log(`    ❌ Persian payment.declared missing placeholders: "${paymentDeclared}"`);
    }

  } catch (error) {
    console.log(`    ❌ Error validating translations: ${error.message}`);
  }
}

testSpecificScenarios();

testUpdatedTranslations();

console.log('\n' + '='.repeat(60));
console.log('🎉 System Message Scenario Testing Complete!');
console.log('\n📋 Next Steps:');
console.log('   1. Start backend server: cd backend && npm run dev');
console.log('   2. Start frontend server: cd frontend && npm run dev');
console.log('   3. Open browser console to see debug logs');
console.log('   4. Navigate to a transaction and perform actions');
console.log('   5. Verify system messages show complete information');
console.log('\n🔍 Debug Information to Look For:');
console.log('   • Backend: Look for [TransactionalChatService] logs with data objects');
console.log('   • Frontend: Look for [SystemLog] logs showing translation processing');
console.log('   • Socket: Look for SYSTEM_MESSAGE_RECEIVE events with data field');
console.log('   • Translation: Verify messages include usernames and amounts');

process.exit(0);
