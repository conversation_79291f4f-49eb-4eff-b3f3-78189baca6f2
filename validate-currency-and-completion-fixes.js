#!/usr/bin/env node

/**
 * Validation script for currency logic and transaction completion fixes
 */

const fs = require('fs');

console.log('🔍 CURRENCY LOGIC & COMPLETION MESSAGE VALIDATION');
console.log('================================================\n');

let allValid = true;
const issues = [];

// Test 1: Backend Service Currency Logic
console.log('1. 💰 Testing Backend Currency Logic Fixes...');

try {
  const chatServiceContent = fs.readFileSync('backend/src/services/transactionalChatService.ts', 'utf8');
  
  // Check first payment currency logic
  if (chatServiceContent.includes('firstPayerIsCurrencyAProvider') && 
      chatServiceContent.includes('firstPayerIsCurrencyAProvider ? transactionForFirstAmount.amountA : transactionForFirstAmount.amountB')) {
    console.log('  ✅ First payment currency logic fixed');
  } else {
    console.log('  ❌ First payment currency logic incorrect');
    issues.push('First payment currency logic incorrect');
    allValid = false;
  }
  
  // Check second payment currency logic
  if (chatServiceContent.includes('secondPayerIsCurrencyAProvider') && 
      chatServiceContent.includes('secondPayerIsCurrencyAProvider ? transactionForAmount.amountA : transactionForAmount.amountB')) {
    console.log('  ✅ Second payment currency logic fixed');
  } else {
    console.log('  ❌ Second payment currency logic incorrect');
    issues.push('Second payment currency logic incorrect');
    allValid = false;
  }
  
  // Check historical reconstruction currency logic
  if (chatServiceContent.includes('firstPayerIsCurrencyAProvider = transaction.agreedFirstPayerId === transaction.currencyAProviderId') &&
      chatServiceContent.includes('secondPayerIsCurrencyAProvider = transaction.agreedFirstPayerId !== transaction.currencyAProviderId')) {
    console.log('  ✅ Historical reconstruction currency logic fixed');
  } else {
    console.log('  ❌ Historical reconstruction currency logic incorrect');
    issues.push('Historical reconstruction currency logic incorrect');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading transactionalChatService.ts: ${error.message}`);
  issues.push(`Error reading transactionalChatService.ts: ${error.message}`);
  allValid = false;
}

// Test 2: Transaction Completion Logic
console.log('\n2. 🎉 Testing Transaction Completion Fixes...');

try {
  const transactionServiceContent = fs.readFileSync('backend/src/services/transactionService.ts', 'utf8');
  
  // Check paymentDueDateForPayer2 variable declaration
  if (transactionServiceContent.includes('let paymentDueDateForPayer2: Date | undefined = undefined;')) {
    console.log('  ✅ paymentDueDateForPayer2 variable properly declared');
  } else {
    console.log('  ❌ paymentDueDateForPayer2 variable declaration missing');
    issues.push('paymentDueDateForPayer2 variable declaration missing');
    allValid = false;
  }
  
  // Check transaction completion system message
  if (transactionServiceContent.includes('systemMessages.transaction.complete') &&
      transactionServiceContent.includes('if (updatedData.status === TransactionStatus.COMPLETED)')) {
    console.log('  ✅ Transaction completion system message implemented');
  } else {
    console.log('  ❌ Transaction completion system message missing');
    issues.push('Transaction completion system message missing');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading transactionService.ts: ${error.message}`);
  issues.push(`Error reading transactionService.ts: ${error.message}`);
  allValid = false;
}

// Test 3: Translation Files
console.log('\n3. 📝 Testing Translation Files...');

try {
  const faTranslations = JSON.parse(fs.readFileSync('frontend/src/locales/fa/systemMessages.json', 'utf8'));
  const enTranslations = JSON.parse(fs.readFileSync('frontend/src/locales/en/systemMessages.json', 'utf8'));
  
  // Check transaction completion message exists
  if (faTranslations.transaction?.complete && enTranslations.transaction?.complete) {
    console.log('  ✅ Transaction completion messages exist in both languages');
  } else {
    console.log('  ❌ Transaction completion messages missing');
    issues.push('Transaction completion messages missing');
    allValid = false;
  }
  
  // Check Persian completion message
  if (faTranslations.transaction?.complete?.includes('🎉')) {
    console.log('  ✅ Persian completion message includes celebration emoji');
  } else {
    console.log('  ❌ Persian completion message missing celebration emoji');
    issues.push('Persian completion message missing celebration emoji');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading translation files: ${error.message}`);
  issues.push(`Error reading translation files: ${error.message}`);
  allValid = false;
}

// Summary
console.log('\n📊 VALIDATION SUMMARY');
console.log('====================');

if (allValid) {
  console.log('🎉 ALL TESTS PASSED! Currency logic and completion message fixes are implemented.');
  console.log('\n✅ Fixed Issues:');
  console.log('   1. First payment currency logic - now uses correct currency for first payer');
  console.log('   2. Second payment currency logic - now uses correct currency for second payer');
  console.log('   3. Historical reconstruction currency logic - properly differentiates currencies');
  console.log('   4. Transaction completion system message - now triggers on COMPLETED status');
  console.log('   5. Backend error fix - paymentDueDateForPayer2 variable properly scoped');
  
  console.log('\n🚀 Expected Results After Refresh:');
  console.log('   • First payment: "اعلام پرداخت اول توسط h برای مبلغ 285.0M IRR ثبت شد."');
  console.log('   • Second payment: "اعلام پرداخت دوم توسط h2 برای مبلغ 500 CAD ثبت شد."');
  console.log('   • Transaction completion: "معامله با موفقیت تکمیل شد. 🎉"');
} else {
  console.log('❌ VALIDATION FAILED! Issues found:');
  issues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });
  process.exit(1);
}

console.log('\n🔄 Testing Instructions:');
console.log('1. Restart the backend server to apply the fixes');
console.log('2. Refresh the transaction page');
console.log('3. Check browser console for correct currency amounts in system messages');
console.log('4. Complete a new transaction to see the completion message');
