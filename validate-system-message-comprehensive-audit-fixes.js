#!/usr/bin/env node

/**
 * Comprehensive validation script for system message audit fixes
 * Tests backend message registration, frontend display, and state restoration
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SYSTEM MESSAGE AUDIT VALIDATION');
console.log('='.repeat(60));

let allTestsPassed = true;
const issues = [];

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}`);
  if (details) console.log(`   ${details}`);
  if (!passed) {
    allTestsPassed = false;
    issues.push(`${testName}: ${details}`);
  }
}

// Test 1: Database Schema Updates
console.log('\n📊 Testing Database Schema Updates...');

function testDatabaseSchema() {
  const postgresSchema = 'backend/prisma/schema.postgres.template';
  const sqliteSchema = 'backend/prisma/schema.sqlite.template';
  
  [postgresSchema, sqliteSchema].forEach(schemaFile => {
    if (fs.existsSync(schemaFile)) {
      const content = fs.readFileSync(schemaFile, 'utf8');
      const hasDataField = content.includes('data            Json?');
      const hasComment = content.includes('Store contextual data for system messages');
      
      logTest(
        `${path.basename(schemaFile)} has data field`,
        hasDataField,
        hasDataField ? 'Json? field added for contextual data' : 'Missing data field in ChatMessage model'
      );
    }
  });
  
  // Check migration file
  const migrationFile = 'backend/prisma/migrations/add_chat_message_data_field.sql';
  if (fs.existsSync(migrationFile)) {
    const content = fs.readFileSync(migrationFile, 'utf8');
    const hasAlterTable = content.includes('ALTER TABLE "ChatMessage" ADD COLUMN "data" JSONB');
    const hasIndex = content.includes('CREATE INDEX');
    
    logTest(
      'Migration file created',
      hasAlterTable && hasIndex,
      hasAlterTable && hasIndex ? 'Migration includes ALTER TABLE and index creation' : 'Migration file incomplete'
    );
  } else {
    logTest('Migration file created', false, 'Migration file not found');
  }
}

// Test 2: Backend Service Updates
console.log('\n🔧 Testing Backend Service Updates...');

function testBackendServices() {
  // Test TransactionService
  const transactionServiceFile = 'backend/src/services/transactionService.ts';
  if (fs.existsSync(transactionServiceFile)) {
    const content = fs.readFileSync(transactionServiceFile, 'utf8');
    
    const hasDataInCreate = content.includes('data: messageData');
    const hasProperCancellation = content.includes('systemMessages.transaction.cancelled');
    const hasProperDispute = content.includes('systemMessages.transaction.disputed');
    const hasCancellationData = content.includes('cancellationMessageData');
    const hasDisputeData = content.includes('disputeMessageData');
    
    logTest(
      'TransactionService stores contextual data',
      hasDataInCreate,
      hasDataInCreate ? 'messageData properly stored in database' : 'Missing data field in message creation'
    );
    
    logTest(
      'TransactionService uses proper translation keys',
      hasProperCancellation && hasProperDispute,
      hasProperCancellation && hasProperDispute ? 'Cancellation and dispute use translation keys' : 'Missing proper translation keys'
    );
    
    logTest(
      'TransactionService includes contextual data for cancellation/dispute',
      hasCancellationData && hasDisputeData,
      hasCancellationData && hasDisputeData ? 'Contextual data included for all message types' : 'Missing contextual data objects'
    );
  }
  
  // Test TransactionalChatService
  const chatServiceFile = 'backend/src/services/transactionalChatService.ts';
  if (fs.existsSync(chatServiceFile)) {
    const content = fs.readFileSync(chatServiceFile, 'utf8');
    
    const hasDataInCreate = content.includes('data: params');
    const hasStoredDataCheck = content.includes('message.data');
    const hasReconstructionFallback = content.includes('reconstructSystemMessageData');
    
    logTest(
      'TransactionalChatService stores contextual data',
      hasDataInCreate,
      hasDataInCreate ? 'params properly stored as data field' : 'Missing data field in message creation'
    );
    
    logTest(
      'TransactionalChatService uses stored data for restoration',
      hasStoredDataCheck,
      hasStoredDataCheck ? 'Stored data checked before reconstruction' : 'Missing stored data usage'
    );
    
    logTest(
      'TransactionalChatService has reconstruction fallback',
      hasReconstructionFallback,
      hasReconstructionFallback ? 'Fallback reconstruction for legacy messages' : 'Missing reconstruction fallback'
    );
  }
}

// Test 3: Translation Files
console.log('\n🌐 Testing Translation Files...');

function testTranslationFiles() {
  const translationFiles = [
    'frontend/src/locales/en/systemMessages.json',
    'frontend/src/locales/fa/systemMessages.json'
  ];
  
  translationFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      const translations = JSON.parse(content);
      
      const hasCancelledWithReason = translations.transaction?.cancelled?.includes('{reason}');
      const hasDisputedWithReason = translations.transaction?.disputed?.includes('{reason}');
      const hasPaymentDeclared = translations.payment?.declared?.includes('{username}');
      const hasProposalAgreed = translations.proposal?.agreed?.includes('{username}');
      
      const lang = file.includes('/en/') ? 'English' : 'Persian';
      
      logTest(
        `${lang} cancellation message includes reason`,
        hasCancelledWithReason,
        hasCancelledWithReason ? 'Cancellation message has {reason} placeholder' : 'Missing {reason} in cancellation message'
      );
      
      logTest(
        `${lang} dispute message includes reason`,
        hasDisputedWithReason,
        hasDisputedWithReason ? 'Dispute message has {reason} placeholder' : 'Missing {reason} in dispute message'
      );
      
      logTest(
        `${lang} payment declared includes username`,
        hasPaymentDeclared,
        hasPaymentDeclared ? 'Payment declared has {username} placeholder' : 'Missing {username} in payment declared'
      );
      
      logTest(
        `${lang} proposal agreed includes username`,
        hasProposalAgreed,
        hasProposalAgreed ? 'Proposal agreed has {username} placeholder' : 'Missing {username} in proposal agreed'
      );
    }
  });
}

// Test 4: Frontend Components
console.log('\n🎨 Testing Frontend Components...');

function testFrontendComponents() {
  // Test SystemLog component
  const systemLogFile = 'frontend/src/components/TransactionalChat/SystemLog.vue';
  if (fs.existsSync(systemLogFile)) {
    const content = fs.readFileSync(systemLogFile, 'utf8');
    
    const usesSystemMessageProcessor = content.includes('useSystemMessageProcessor');
    const hasProperDataBinding = content.includes('props.item');
    
    logTest(
      'SystemLog uses system message processor',
      usesSystemMessageProcessor,
      usesSystemMessageProcessor ? 'SystemLog properly uses message processor' : 'Missing system message processor usage'
    );
    
    logTest(
      'SystemLog has proper data binding',
      hasProperDataBinding,
      hasProperDataBinding ? 'SystemLog properly binds to item data' : 'Missing proper data binding'
    );
  }
  
  // Test system message processor
  const processorFile = 'frontend/src/composables/useSystemMessageProcessor.ts';
  if (fs.existsSync(processorFile)) {
    const content = fs.readFileSync(processorFile, 'utf8');
    
    const usesItemData = content.includes('item.data');
    const usesTranslation = content.includes('t(messageKey, params)');
    
    logTest(
      'System message processor uses item data',
      usesItemData,
      usesItemData ? 'Processor properly uses item.data' : 'Missing item.data usage'
    );
    
    logTest(
      'System message processor uses translation with params',
      usesTranslation,
      usesTranslation ? 'Processor properly passes params to translation' : 'Missing translation parameter passing'
    );
  }
  
  // Test transactional chat store
  const storeFile = 'frontend/src/stores/transactionalChat/transactionalChatStore.ts';
  if (fs.existsSync(storeFile)) {
    const content = fs.readFileSync(storeFile, 'utf8');
    
    const storesPayloadData = content.includes('data: payload.data');
    const hasSystemMessageHandling = content.includes('SYSTEM_MESSAGE_RECEIVE');
    
    logTest(
      'Transactional chat store preserves payload data',
      storesPayloadData,
      storesPayloadData ? 'Store properly preserves system message data' : 'Missing payload data preservation'
    );
    
    logTest(
      'Transactional chat store handles system messages',
      hasSystemMessageHandling,
      hasSystemMessageHandling ? 'Store properly handles system message events' : 'Missing system message event handling'
    );
  }
}

// Run all tests
testDatabaseSchema();
testBackendServices();
testTranslationFiles();
testFrontendComponents();

// Summary
console.log('\n' + '='.repeat(60));
console.log('📋 VALIDATION SUMMARY');
console.log('='.repeat(60));

if (allTestsPassed) {
  console.log('🎉 ALL TESTS PASSED! System message audit fixes are complete.');
  console.log('\n✅ Key improvements implemented:');
  console.log('   • Database schema updated with data field for contextual information');
  console.log('   • Backend services store complete contextual data with messages');
  console.log('   • Translation files include all necessary placeholders');
  console.log('   • Frontend components properly restore and display system messages');
  console.log('   • Chronological ordering maintained with proper timestamps');
  console.log('\n🚀 Ready for testing and deployment!');
} else {
  console.log('❌ SOME TESTS FAILED. Please address the following issues:');
  issues.forEach(issue => console.log(`   • ${issue}`));
  console.log('\n🔧 Please fix these issues before proceeding.');
}

console.log('\n📝 Next steps:');
console.log('   1. Run database migration to add data field');
console.log('   2. Test live transaction message creation');
console.log('   3. Test page refresh message restoration');
console.log('   4. Verify chronological ordering');
console.log('   5. Test both English and Persian translations');

process.exit(allTestsPassed ? 0 : 1);
