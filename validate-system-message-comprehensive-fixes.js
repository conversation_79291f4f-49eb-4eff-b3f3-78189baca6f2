#!/usr/bin/env node

/**
 * Comprehensive validation script for system message fixes
 * Tests all the issues identified and fixed in the transaction flow
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SYSTEM MESSAGE VALIDATION');
console.log('==========================================\n');

let allValid = true;
const issues = [];

// Test 1: Translation Key Consistency
console.log('1. 📝 Testing Translation Key Consistency...');

const translationFiles = [
  { path: 'frontend/src/locales/en/transactionalChat.json', lang: 'EN' },
  { path: 'frontend/src/locales/fa/transactionalChat.json', lang: 'FA' },
  { path: 'frontend/src/locales/en/systemMessages.json', lang: 'EN' },
  { path: 'frontend/src/locales/fa/systemMessages.json', lang: 'FA' }
];

translationFiles.forEach(({ path: filePath, lang }) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    // Check firstPaymentDeclared uses {username}
    const firstPayment = translations.systemLogs?.firstPaymentDeclared || 
                        translations.transactionalChat?.systemLogs?.firstPaymentDeclared;
    if (firstPayment && firstPayment.includes('{username}')) {
      console.log(`  ✅ ${lang} firstPaymentDeclared uses {username}`);
    } else if (firstPayment) {
      console.log(`  ❌ ${lang} firstPaymentDeclared missing {username}: ${firstPayment}`);
      issues.push(`${lang} firstPaymentDeclared missing {username}`);
      allValid = false;
    }
    
    // Check secondPaymentDeclared uses {username}
    const secondPayment = translations.systemLogs?.secondPaymentDeclared || 
                         translations.transactionalChat?.systemLogs?.secondPaymentDeclared;
    if (secondPayment && secondPayment.includes('{username}')) {
      console.log(`  ✅ ${lang} secondPaymentDeclared uses {username}`);
    } else if (secondPayment) {
      console.log(`  ❌ ${lang} secondPaymentDeclared missing {username}: ${secondPayment}`);
      issues.push(`${lang} secondPaymentDeclared missing {username}`);
      allValid = false;
    }
    
    // Check counter-offer translation exists
    const counterOffer = translations.proposal?.counterOffer;
    if (counterOffer && counterOffer.includes('{username}')) {
      console.log(`  ✅ ${lang} counterOffer translation exists with {username}`);
    } else if (filePath.includes('systemMessages')) {
      console.log(`  ❌ ${lang} counterOffer translation missing or invalid`);
      issues.push(`${lang} counterOffer translation missing`);
      allValid = false;
    }
    
    // Check transaction completion message
    const transactionComplete = translations.transaction?.complete;
    if (transactionComplete) {
      console.log(`  ✅ ${lang} transaction completion message exists`);
    } else if (filePath.includes('systemMessages')) {
      console.log(`  ❌ ${lang} transaction completion message missing`);
      issues.push(`${lang} transaction completion message missing`);
      allValid = false;
    }
    
  } catch (error) {
    console.log(`  ❌ Error reading ${lang} file: ${error.message}`);
    issues.push(`Error reading ${lang} file: ${error.message}`);
    allValid = false;
  }
});

// Test 2: Backend Service Logic
console.log('\n2. 🔧 Testing Backend Service Logic...');

// Check transactionalChatService.ts fixes
try {
  const chatServiceContent = fs.readFileSync('backend/src/services/transactionalChatService.ts', 'utf8');
  
  // Check firstPaymentDeclared includes username data
  if (chatServiceContent.includes('firstPayerUsername') && 
      chatServiceContent.includes('{ username: firstPayerUsername, amount }')) {
    console.log('  ✅ firstPaymentDeclared includes username data');
  } else {
    console.log('  ❌ firstPaymentDeclared missing username data');
    issues.push('firstPaymentDeclared missing username data');
    allValid = false;
  }
  
  // Check paymentExpectedByPayer1 is used instead of paymentDueDateForPayer1
  if (chatServiceContent.includes('paymentExpectedByPayer1?.toLocaleString()')) {
    console.log('  ✅ Due date uses correct field paymentExpectedByPayer1');
  } else {
    console.log('  ❌ Due date field incorrect');
    issues.push('Due date field incorrect');
    allValid = false;
  }
  
  // Check username differentiation logic
  if (chatServiceContent.includes('messageIdHash') && 
      chatServiceContent.includes('isUserA ? userAName : userBName')) {
    console.log('  ✅ Username differentiation logic implemented');
  } else {
    console.log('  ❌ Username differentiation logic missing');
    issues.push('Username differentiation logic missing');
    allValid = false;
  }
  
  // Check chronological order fix
  if (chatServiceContent.includes('setTimeout(resolve, 100)')) {
    console.log('  ✅ Chronological order fix implemented');
  } else {
    console.log('  ❌ Chronological order fix missing');
    issues.push('Chronological order fix missing');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading transactionalChatService.ts: ${error.message}`);
  issues.push(`Error reading transactionalChatService.ts: ${error.message}`);
  allValid = false;
}

// Check transactionService.ts fixes
try {
  const transactionServiceContent = fs.readFileSync('backend/src/services/transactionService.ts', 'utf8');
  
  // Check transaction completion message
  if (transactionServiceContent.includes('systemMessages.transaction.complete')) {
    console.log('  ✅ Transaction completion system message added');
  } else {
    console.log('  ❌ Transaction completion system message missing');
    issues.push('Transaction completion system message missing');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading transactionService.ts: ${error.message}`);
  issues.push(`Error reading transactionService.ts: ${error.message}`);
  allValid = false;
}

// Check payerNegotiationService.ts fixes
try {
  const negotiationServiceContent = fs.readFileSync('backend/src/services/payerNegotiationService.ts', 'utf8');
  
  // Check counter-offer system message
  if (negotiationServiceContent.includes('systemMessages.proposal.counterOffer')) {
    console.log('  ✅ Counter-offer system message added');
  } else {
    console.log('  ❌ Counter-offer system message missing');
    issues.push('Counter-offer system message missing');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading payerNegotiationService.ts: ${error.message}`);
  issues.push(`Error reading payerNegotiationService.ts: ${error.message}`);
  allValid = false;
}

// Test 3: Currency Logic Fix
console.log('\n3. 💰 Testing Currency Logic...');

try {
  const chatServiceContent = fs.readFileSync('backend/src/services/transactionalChatService.ts', 'utf8');
  
  // Check second payment currency logic
  if (chatServiceContent.includes('// For second payment, the user is the second payer') &&
      chatServiceContent.includes('isFirstPayer ? transactionForAmount.amountA : transactionForAmount.amountB')) {
    console.log('  ✅ Second payment currency logic fixed');
  } else {
    console.log('  ❌ Second payment currency logic incorrect');
    issues.push('Second payment currency logic incorrect');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error checking currency logic: ${error.message}`);
  issues.push(`Error checking currency logic: ${error.message}`);
  allValid = false;
}

// Summary
console.log('\n📊 VALIDATION SUMMARY');
console.log('====================');

if (allValid) {
  console.log('🎉 ALL TESTS PASSED! All system message issues have been fixed.');
  console.log('\n✅ Fixed Issues:');
  console.log('   1. Translation key mismatch ({name} → {username})');
  console.log('   2. Missing username data for first payment declaration');
  console.log('   3. Wrong currency in second payment');
  console.log('   4. TBD due date (paymentDueDateForPayer1 → paymentExpectedByPayer1)');
  console.log('   5. Duplicate username issue (improved differentiation logic)');
  console.log('   6. Missing transaction completion message');
  console.log('   7. Chronological order issue (added delay)');
  console.log('   8. Missing counter-offer system messages');
} else {
  console.log('❌ VALIDATION FAILED! Issues found:');
  issues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });
  process.exit(1);
}

console.log('\n🚀 Ready for testing! Refresh the transaction page to see the fixes.');
