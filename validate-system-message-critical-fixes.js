#!/usr/bin/env node

/**
 * Validation script for critical system message fixes
 * 
 * This script validates that the critical issues identified in the system messages
 * have been properly addressed:
 * 
 * 1. ✅ Payment amount currency fix (first payer sends their own currency)
 * 2. ✅ Username accuracy fix (direct database lookup instead of context helper)
 * 3. ✅ Duplicate message prevention (removed redundant system message calls)
 * 4. ✅ Historical message reconstruction fix (correct payment amounts)
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Critical System Message Fixes...\n');

let allValid = true;
const issues = [];

// 1. Validate payment amount fix in transactionService.ts
console.log('1. 📊 Checking payment amount currency fix...');
try {
  const transactionServicePath = 'backend/src/services/transactionService.ts';
  const content = fs.readFileSync(transactionServicePath, 'utf8');
  
  // Check for the fixed payment amount logic
  const hasCorrectPaymentLogic = content.includes('isFirstPayerCurrencyAProvider ? transaction.amountA : transaction.amountB') &&
                                 content.includes('isFirstPayerCurrencyAProvider ? transaction.currencyA : transaction.currencyB') &&
                                 content.includes('First payer sends their own currency');
  
  if (hasCorrectPaymentLogic) {
    console.log('  ✅ Payment amount currency logic fixed');
  } else {
    console.log('  ❌ Payment amount currency logic not found');
    issues.push('Payment amount currency logic not properly implemented');
    allValid = false;
  }
  
  // Check for proper currency determination logic
  const hasProperCurrencyLogic = content.includes('isCurrentUserFirstPayer') &&
                                content.includes('paymentAmount') &&
                                content.includes('paymentCurrency');
  
  if (hasProperCurrencyLogic) {
    console.log('  ✅ Proper currency determination logic implemented');
  } else {
    console.log('  ❌ Currency determination logic missing');
    issues.push('Currency determination logic not found');
    allValid = false;
  }
  
} catch (error) {
  console.log('  ❌ Error reading transactionService.ts:', error.message);
  issues.push('Could not validate transactionService.ts');
  allValid = false;
}

// 2. Validate username accuracy fix in payerNegotiationService.ts
console.log('\n2. 👤 Checking username accuracy fix...');
try {
  const payerNegotiationPath = 'backend/src/services/payerNegotiationService.ts';
  const content = fs.readFileSync(payerNegotiationPath, 'utf8');
  
  // Check for direct database lookup instead of context helper
  const hasDirectUserLookup = content.includes('await this.prisma.user.findUnique') &&
                             content.includes('where: { id: userId }') &&
                             content.includes('select: { username: true, email: true }') &&
                             content.includes('Agreement by user');
  
  if (hasDirectUserLookup) {
    console.log('  ✅ Direct username lookup implemented');
  } else {
    console.log('  ❌ Direct username lookup not found');
    issues.push('Direct username lookup not implemented');
    allValid = false;
  }
  
} catch (error) {
  console.log('  ❌ Error reading payerNegotiationService.ts:', error.message);
  issues.push('Could not validate payerNegotiationService.ts');
  allValid = false;
}

// 3. Validate duplicate message prevention in transactionalChatService.ts
console.log('\n3. 🚫 Checking duplicate message prevention...');
try {
  const chatServicePath = 'backend/src/services/transactionalChatService.ts';
  const content = fs.readFileSync(chatServicePath, 'utf8');
  
  // Check that duplicate system message calls have been removed
  const declareFirstPayerSection = content.match(/case 'declareFirstPayerPayment':([\s\S]*?)break;/);
  const declareSecondPayerSection = content.match(/case 'declareSecondPayerPayment':([\s\S]*?)break;/);
  
  if (declareFirstPayerSection) {
    const firstPayerCode = declareFirstPayerSection[1];
    const hasOnlyMainCall = firstPayerCode.includes('transactionService.declarePayment') &&
                           !firstPayerCode.includes('transactionalChat.systemLogs.firstPaymentDeclared');
    
    if (hasOnlyMainCall) {
      console.log('  ✅ First payer duplicate message removed');
    } else {
      console.log('  ❌ First payer still has duplicate system message');
      issues.push('First payer duplicate system message not removed');
      allValid = false;
    }
  }
  
  if (declareSecondPayerSection) {
    const secondPayerCode = declareSecondPayerSection[1];
    const hasOnlyMainCall = secondPayerCode.includes('transactionService.declarePayment') &&
                           !secondPayerCode.includes('transactionalChat.systemLogs.secondPaymentDeclared');
    
    if (hasOnlyMainCall) {
      console.log('  ✅ Second payer duplicate message removed');
    } else {
      console.log('  ❌ Second payer still has duplicate system message');
      issues.push('Second payer duplicate system message not removed');
      allValid = false;
    }
  }
  
} catch (error) {
  console.log('  ❌ Error reading transactionalChatService.ts:', error.message);
  issues.push('Could not validate transactionalChatService.ts');
  allValid = false;
}

// 4. Validate historical message reconstruction fix
console.log('\n4. 📜 Checking historical message reconstruction fix...');
try {
  const chatServicePath = 'backend/src/services/transactionalChatService.ts';
  const content = fs.readFileSync(chatServicePath, 'utf8');
  
  // Check for fixed historical reconstruction logic
  const hasFixedHistoricalLogic = content.includes('isFirstPayerCurrencyAProvider ? transaction.amountA : transaction.amountB') &&
                                 content.includes('First payer sends their own currency, not the other currency') &&
                                 content.includes('reconstructSystemMessageData');
  
  if (hasFixedHistoricalLogic) {
    console.log('  ✅ Historical message reconstruction fixed');
  } else {
    console.log('  ❌ Historical message reconstruction not fixed');
    issues.push('Historical message reconstruction logic not updated');
    allValid = false;
  }
  
  // Check for improved proposal agreement logic
  const hasImprovedProposalLogic = content.includes('deterministic approach') &&
                                  content.includes('messageTimestamp') &&
                                  content.includes('Historical proposal agreement');
  
  if (hasImprovedProposalLogic) {
    console.log('  ✅ Improved proposal agreement logic implemented');
  } else {
    console.log('  ❌ Proposal agreement logic not improved');
    issues.push('Proposal agreement logic not improved');
    allValid = false;
  }
  
} catch (error) {
  console.log('  ❌ Error reading transactionalChatService.ts for historical fixes:', error.message);
  issues.push('Could not validate historical message fixes');
  allValid = false;
}

// 5. Check that translation files support the required placeholders
console.log('\n5. 🌐 Checking translation file compatibility...');
const translationFiles = [
  { path: 'frontend/src/locales/en/systemMessages.json', lang: 'EN' },
  { path: 'frontend/src/locales/fa/systemMessages.json', lang: 'FA' }
];

translationFiles.forEach(({ path: filePath, lang }) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    // Check payment.declared has all required placeholders
    const paymentDeclared = translations.payment?.declared;
    if (paymentDeclared && 
        paymentDeclared.includes('{username}') && 
        paymentDeclared.includes('{amount}') && 
        paymentDeclared.includes('{otherUser}')) {
      console.log(`  ✅ ${lang} payment.declared has all placeholders`);
    } else {
      console.log(`  ❌ ${lang} payment.declared missing placeholders`);
      issues.push(`${lang} payment.declared missing required placeholders`);
      allValid = false;
    }
    
    // Check proposal.agreed has username placeholder
    const proposalAgreed = translations.proposal?.agreed;
    if (proposalAgreed && proposalAgreed.includes('{username}')) {
      console.log(`  ✅ ${lang} proposal.agreed has username placeholder`);
    } else {
      console.log(`  ❌ ${lang} proposal.agreed missing username placeholder`);
      issues.push(`${lang} proposal.agreed missing username placeholder`);
      allValid = false;
    }
    
  } catch (error) {
    console.log(`  ❌ Error reading ${lang} translation file: ${error.message}`);
    issues.push(`Error reading ${lang} translation file`);
    allValid = false;
  }
});

// Final validation result
console.log('\n' + '='.repeat(60));
if (allValid) {
  console.log('🎉 ALL CRITICAL SYSTEM MESSAGE FIXES VALIDATED SUCCESSFULLY!');
  console.log('\n✅ Fixed Issues:');
  console.log('   • Payment amount shows correct currency (sender\'s currency)');
  console.log('   • Username accuracy improved with direct database lookup');
  console.log('   • Duplicate system messages eliminated');
  console.log('   • Historical message reconstruction corrected');
  console.log('   • Translation files support all required placeholders');
  console.log('\n🚀 System messages should now display accurate information!');
  process.exit(0);
} else {
  console.log('❌ VALIDATION FAILED - Issues found:');
  issues.forEach(issue => console.log(`   • ${issue}`));
  console.log('\n🔧 Please address the above issues before deployment.');
  process.exit(1);
}
