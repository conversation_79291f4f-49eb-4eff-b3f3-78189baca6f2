#!/usr/bin/env node

/**
 * Validation script for System Message Translation Fixes
 * 
 * This script validates that all the critical issues identified in the screenshot
 * have been properly addressed in the codebase.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating System Message Translation Fixes...\n');

let allValid = true;
const issues = [];

// 1. Check that translation files contain all required keys
console.log('📋 Checking Translation Files...');

const translationFiles = [
  { path: 'frontend/src/locales/en/systemMessages.json', lang: 'English' },
  { path: 'frontend/src/locales/fa/systemMessages.json', lang: 'Persian' }
];

const requiredKeys = [
  'payment.declared',
  'payment.confirmedFirst',
  'payment.confirmedSecond',
  'proposal.from',
  'proposal.agreed',
  'proposal.bothAgreed',
  'proposal.bothAgreedSystemDesignated',
  'transaction.complete',
  'transactionalChat.systemLogs.paymentDetailsProvided',
  'transactionalChat.systemLogs.readyToNegotiate',
  'transactionalChat.systemLogs.secondPaymentDeclared'
];

translationFiles.forEach(({ path: filePath, lang }) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    console.log(`  ✅ ${lang} translation file loaded successfully`);
    
    // Check for required keys
    const missingKeys = [];
    requiredKeys.forEach(key => {
      const keyParts = key.split('.');
      let current = translations;
      
      for (const part of keyParts) {
        if (!current || !current[part]) {
          missingKeys.push(key);
          break;
        }
        current = current[part];
      }
    });
    
    if (missingKeys.length === 0) {
      console.log(`  ✅ All required translation keys present in ${lang}`);
    } else {
      console.log(`  ❌ Missing keys in ${lang}: ${missingKeys.join(', ')}`);
      issues.push(`Missing translation keys in ${lang}: ${missingKeys.join(', ')}`);
      allValid = false;
    }
    
    // Check that payment.declared includes {username} and {amount} placeholders
    const paymentDeclared = translations.systemMessages?.payment?.declared;
    if (paymentDeclared) {
      if (paymentDeclared.includes('{username}') && paymentDeclared.includes('{amount}')) {
        console.log(`  ✅ Payment declared message includes required placeholders in ${lang}`);
      } else {
        console.log(`  ❌ Payment declared message missing placeholders in ${lang}`);
        issues.push(`Payment declared message missing {username} or {amount} in ${lang}`);
        allValid = false;
      }
    }
    
  } catch (error) {
    console.log(`  ❌ Error reading ${lang} translation file: ${error.message}`);
    issues.push(`Error reading ${lang} translation file: ${error.message}`);
    allValid = false;
  }
});

// 2. Check backend services for proper data passing
console.log('\n🔧 Checking Backend Services...');

const backendFiles = [
  'backend/src/services/transactionService.ts',
  'backend/src/services/transactionalChatService.ts',
  'backend/src/services/payerNegotiationService.ts'
];

backendFiles.forEach(filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);
    
    console.log(`  📁 Checking ${fileName}...`);
    
    // Check for createAndEmitSystemMessage calls with data parameter
    const systemMessageCalls = content.match(/createAndEmitSystemMessage\([^)]+\)/g) || [];
    const callsWithData = systemMessageCalls.filter(call => 
      call.split(',').length >= 4 // Should have at least 4 parameters including data
    );
    
    if (systemMessageCalls.length > 0) {
      console.log(`    ✅ Found ${systemMessageCalls.length} system message calls`);
      console.log(`    ✅ ${callsWithData.length} calls include data parameter`);
    }
    
    // Check for formatAmount method
    if (content.includes('formatAmount')) {
      console.log(`    ✅ formatAmount method found`);
    }
    
    // Check for username extraction
    if (content.includes('username') && content.includes('findUnique')) {
      console.log(`    ✅ Username extraction logic found`);
    }
    
  } catch (error) {
    console.log(`  ❌ Error reading ${filePath}: ${error.message}`);
    issues.push(`Error reading ${filePath}: ${error.message}`);
    allValid = false;
  }
});

// 3. Check frontend SystemLog component
console.log('\n🎨 Checking Frontend Components...');

try {
  const systemLogContent = fs.readFileSync('frontend/src/components/TransactionalChat/SystemLog.vue', 'utf8');
  
  console.log('  📁 Checking SystemLog.vue...');
  
  // Check for data field usage
  if (systemLogContent.includes('props.item.data')) {
    console.log('    ✅ Component accesses data field from props');
  } else {
    console.log('    ❌ Component does not access data field');
    issues.push('SystemLog component does not access data field');
    allValid = false;
  }
  
  // Check for translation key detection
  if (systemLogContent.includes('isTranslationKey')) {
    console.log('    ✅ Translation key detection implemented');
  } else {
    console.log('    ❌ Translation key detection missing');
    issues.push('Translation key detection missing in SystemLog');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading SystemLog.vue: ${error.message}`);
  issues.push(`Error reading SystemLog.vue: ${error.message}`);
  allValid = false;
}

// 4. Check store for data field handling
try {
  const storeContent = fs.readFileSync('frontend/src/stores/transactionalChat/transactionalChatStore.ts', 'utf8');
  
  console.log('  📁 Checking transactionalChatStore.ts...');
  
  // Check for data field in system message handling
  if (storeContent.includes('data: payload.data')) {
    console.log('    ✅ Store includes data field in system messages');
  } else {
    console.log('    ❌ Store missing data field in system messages');
    issues.push('Store missing data field in system messages');
    allValid = false;
  }
  
} catch (error) {
  console.log(`  ❌ Error reading store: ${error.message}`);
  issues.push(`Error reading store: ${error.message}`);
  allValid = false;
}

// 5. Summary
console.log('\n' + '='.repeat(60));

if (allValid) {
  console.log('🎉 All validations passed! System message fixes are complete.');
  console.log('\n📋 Summary of Fixes Applied:');
  console.log('   ✅ Added missing data parameters to backend system message calls');
  console.log('   ✅ Enhanced translation files with contextual placeholders');
  console.log('   ✅ Fixed username extraction in payment details submission');
  console.log('   ✅ Added formatAmount helper for currency formatting');
  console.log('   ✅ Ensured frontend properly processes data field');
  console.log('   ✅ Added missing translation keys for all message types');
  console.log('\n🔧 Expected Results:');
  console.log('   • Messages like "پیشنهاد توسط پذیرفته شد" should now show usernames');
  console.log('   • Payment messages should include amounts like "28.5M IRR"');
  console.log('   • All system messages should have complete contextual information');
  console.log('   • Counter-proposals should generate appropriate system messages');
} else {
  console.log('❌ Some validations failed. Issues found:');
  issues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });
  console.log('\nPlease address these issues before testing.');
}

console.log('\n🧪 Next Steps:');
console.log('   1. Start the backend and frontend servers');
console.log('   2. Create a test transaction and go through the complete flow');
console.log('   3. Verify that all system messages show complete information');
console.log('   4. Test in both English and Persian languages');
console.log('   5. Check that IRR amounts display in millions format');

process.exit(allValid ? 0 : 1);
